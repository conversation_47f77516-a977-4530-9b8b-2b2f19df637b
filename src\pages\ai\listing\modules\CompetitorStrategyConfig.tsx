import { 
  Input,
  Empty, 
  Typography, 
} from 'antd';
import { 
  PlusOutlined, 
  InfoCircleOutlined,
} from '@ant-design/icons';
import { Comp<PERSON><PERSON>, CompAsinList, AuthAsinAdd<PERSON>ey<PERSON>ord, AuthAsinDeleteKeyWord, AuthAsinShowFilteredKeyword, AuthAsinShowFilteredUserShopCompetitiveLog } from '@/service/api';
import { Icon } from '@iconify/react';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Title, Text } = Typography;

// 定义组件Props类型
interface CompetitorStrategyConfigProps {
  countryCode: string;
  pAsin: string;
}

// 定义ASIN项的类型
interface AsinItem {
  asin: string;
  addTime?: string;
  addAccount?: string;
}

// 定义分页数据类型
interface PaginationState {
  current: number;
  pageSize: number;
  total: number;
  maxPage: number;
}

// 定义操作类型选项
const operationTypes = [
  { label: '全部', value: 'all' },
  { label: '添加', value: 'ADD' },
  { label: '删除', value: 'DEL' }
];

// 定义顶级分类
const topCategories = [
  {
    key: 'asin',
    label: '竞品ASIN',
    children: [
      { key: 'positive', label: '投放竞品ASIN' },
      { key: 'negative', label: '否定竞品ASIN' }
    ]
  },
  {
    key: 'keyword',
    label: '关键词',
    children: [
      { key: 'positive', label: '投放关键词' },
      { key: 'negative', label: '否定关键词' }
    ]
  },
  {
    key: 'history',
    label: '历史记录',
    children: []
  }
];

// 修改样式常量
const guideStyles = {
  guideBox: {
    marginTop: 16,
    padding: 16,
    background: '#f8f9fe',
    borderRadius: 8,
    border: '1px solid #e6e8f0'
  },
  title: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: 12,
    color: '#1a1a1a',
    fontWeight: 500
  },
  icon: {
    color: '#2f54eb',
    fontSize: '18px',
    display: 'flex',
    alignItems: 'center'
  },
  warningIcon: {
    color: '#1a1a1a',
    fontSize: '16px',
    display: 'flex',
    alignItems: 'center',
    marginRight: '8px'
  },
  bulletPoint: {
    marginLeft: 24,
    marginBottom: 8,
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  subBulletPoint: {
    marginLeft: 48,
    marginBottom: 6,
    color: '#666'
  },
  warningBox: {
    marginTop: 12,
    padding: 12,
    background: '#f8f9fe',
    border: '1px solid #e6e8f0',
    borderRadius: 6
  },
  textWrapper: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  modalTitle: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '16px',
    color: '#1a1a1a'
  },
  aiIcon: {
    color: '#2F54EB',
    fontSize: '20px',
    display: 'flex',
    alignItems: 'center'
  },
  infoSection: {
    marginTop: 12,
    padding: 12,
    background: '#f8f9fe',
    border: '1px solid #e6e8f0',
    borderRadius: 6
  }
};

// 否定ASIN指南组件
const NegativeAsinGuide = () => (
  <>
    <div style={guideStyles.bulletPoint}>
      <Text style={{ flex: 1 }}>
        优先依赖AI系统自动否定竞品ASIN，手动操作仅用于补充
      </Text>
    </div>
    <div style={guideStyles.bulletPoint}>
      <Text style={{ flex: 1 }}>
        否定竞品ASIN对应亚马逊广告后台的"精准否定"功能
      </Text>
    </div>
    
    <div style={guideStyles.warningBox}>
      <div style={guideStyles.textWrapper}>
        <InfoCircleOutlined style={guideStyles.warningIcon} />
        <Text strong style={{ flex: 1 }}>添加否定ASIN前，请务必仔细确认：</Text>
      </div>
      <Text type="secondary" style={{ marginLeft: '24px', marginTop: '8px', display: 'block' }}>
        被否定的ASIN与您推广的商品不存在替代/互补关系。如果将有关联的竞品ASIN加入否定列表，将导致：
      </Text>
      <div style={{ marginLeft: '24px', marginTop: '8px' }}>
        <div style={guideStyles.subBulletPoint}>• 系统无法触达相关搜索流量</div>
        <div style={guideStyles.subBulletPoint}>• 错失跨商品关联推荐位曝光</div>
        <div style={guideStyles.subBulletPoint}>• 降低广告组整体转化率</div>
      </div>
    </div>

    <div style={guideStyles.infoSection}>
      <div style={guideStyles.textWrapper}>
        <InfoCircleOutlined style={guideStyles.warningIcon} />
        <Text strong style={{ flex: 1 }}>操作限制：</Text>
      </div>
      <Text type="secondary" style={{ marginLeft: '24px', marginTop: '8px', display: 'block' }}>
        单次手动否定不超过10个ASIN，避免过度限制AI系统的优化空间，否则将导致AI模型学习数据量骤减，优化效果下降
      </Text>
    </div>
  </>
);

// 否定关键词指南组件
const NegativeKeywordGuide = () => (
  <>
    <div style={guideStyles.bulletPoint}>
      <Text style={{ flex: 1 }}>
        优先依赖AI系统自动否定关键词，手动操作仅用于补充
      </Text>
    </div>
    <div style={guideStyles.bulletPoint}>
      <Text style={{ flex: 1 }}>
        否定关键词对应亚马逊广告后台的"精准否定"功能
      </Text>
    </div>
    
    <div style={guideStyles.warningBox}>
      <div style={guideStyles.textWrapper}>
        <InfoCircleOutlined style={guideStyles.warningIcon} />
        <Text strong style={{ flex: 1 }}>添加否定关键词前，请务必仔细确认：</Text>
      </div>
      <Text type="secondary" style={{ marginLeft: '24px', marginTop: '8px', display: 'block' }}>
        被否定的关键词与您推广的商品不存在关联关系。如果将有关联的关键词加入否定列表，将导致：
      </Text>
      <div style={{ marginLeft: '24px', marginTop: '8px' }}>
        <div style={guideStyles.subBulletPoint}>• 系统无法触达相关搜索流量</div>
        <div style={guideStyles.subBulletPoint}>• 降低广告组整体转化率</div>
      </div>
    </div>

    <div style={guideStyles.infoSection}>
      <div style={guideStyles.textWrapper}>
        <InfoCircleOutlined style={guideStyles.warningIcon} />
        <Text strong style={{ flex: 1 }}>操作限制：</Text>
      </div>
      <Text type="secondary" style={{ marginLeft: '24px', marginTop: '8px', display: 'block' }}>
        单次手动否定不超过10个关键词，避免过度限制AI系统的优化空间，否则将导致AI模型学习数据量骤减，优化效果下降
      </Text>
      <div style={{ marginLeft: '24px', marginTop: '8px' }}>
        <div style={guideStyles.subBulletPoint}>• 每个关键词的长度不得超过80个字符(包括空格)</div>
        <div style={guideStyles.subBulletPoint}>• 关键词中不得包含上标与下标符号、表情符号或特殊字符(例如:@、#、*、$ 等)</div>
      </div>
    </div>
  </>
);

// 投放关键词指南组件
const PositiveKeywordGuide = () => (
  <>
    <div style={guideStyles.bulletPoint}>
      <Text style={{ flex: 1 }}>
        AI系统具有自动识别和添加关键词的功能，手动添加关键词将作为关键词探索的补充，以提升广告效果
      </Text>
    </div>
    <div style={guideStyles.infoSection}>
      <div style={guideStyles.textWrapper}>
        <InfoCircleOutlined style={guideStyles.warningIcon} />
        <Text strong style={{ flex: 1 }}>关键词格式要求：</Text>
      </div>
      <div style={{ marginLeft: '24px', marginTop: '8px' }}>
        <div style={guideStyles.subBulletPoint}>• 每个关键词的长度不得超过80个字符(包括空格)</div>
        <div style={guideStyles.subBulletPoint}>• 关键词中不得包含上标与下标符号、表情符号或特殊字符(例如:@、#、*、$ 等)</div>
      </div>
    </div>
  </>
);

/**
 * 竞品策略配置组件
 */
const CompetitorStrategyConfig: React.FC<CompetitorStrategyConfigProps> = ({ countryCode, pAsin }) => {
  // 控制主弹窗显示
  const [isModalVisible, setIsModalVisible] = useState(false);
  // 控制添加ASIN弹窗显示
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  // 当前活动标签页
  const [activeTab, setActiveTab] = useState('1');
  // 搜索关键词
  const [searchText, setSearchText] = useState('');
  // 添加ASIN文本
  const [addAsinText, setAddAsinText] = useState('');
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 竞品ASIN列表
  const [competitorAsins, setCompetitorAsins] = useState<AsinItem[]>([]);
  // 否定竞品ASIN列表
  const [negativeAsins, setNegativeAsins] = useState<AsinItem[]>([]);
  
  // 添加分页状态
  const [competitorPagination, setCompetitorPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 20,
    total: 0,
    maxPage: 1
  });
  
  const [negativePagination, setNegativePagination] = useState<PaginationState>({
    current: 1,
    pageSize: 20,
    total: 0,
    maxPage: 1
  });

  // 添加新的状态
  const [topCategory, setTopCategory] = useState('asin');
  const [subCategory, setSubCategory] = useState('positive');
  const [operationType, setOperationType] = useState('all');

  // 添加关键词列表状态
  const [positiveKeywords, setPositiveKeywords] = useState<any[]>([]);
  const [negativeKeywords, setNegativeKeywords] = useState<any[]>([]);
  const [historyRecords, setHistoryRecords] = useState<any[]>([]);
  
  // 添加关键词分页状态
  const [positiveKeywordPagination, setPositiveKeywordPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 20,
    total: 0,
    maxPage: 1
  });
  
  const [negativeKeywordPagination, setNegativeKeywordPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 20,
    total: 0,
    maxPage: 1
  });
  
  const [historyPagination, setHistoryPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 20,
    total: 0,
    maxPage: 1
  });

  // 获取当前标签页的分页状态
  const getCurrentPagination = () => {
    if (topCategory === 'asin') {
      return subCategory === 'positive' ? competitorPagination : negativePagination;
    } else if (topCategory === 'keyword') {
      return subCategory === 'positive' ? positiveKeywordPagination : negativeKeywordPagination;
    } else {
      return historyPagination;
    }
  };

  // 设置当前标签页的分页状态
  const setCurrentPagination = (newPagination: Partial<PaginationState>) => {
    if (topCategory === 'asin') {
      if (subCategory === 'positive') {
        setCompetitorPagination(prev => ({ ...prev, ...newPagination }));
      } else {
        setNegativePagination(prev => ({ ...prev, ...newPagination }));
      }
    } else if (topCategory === 'keyword') {
      if (subCategory === 'positive') {
        setPositiveKeywordPagination(prev => ({ ...prev, ...newPagination }));
      } else {
        setNegativeKeywordPagination(prev => ({ ...prev, ...newPagination }));
      }
    } else {
      setHistoryPagination(prev => ({ ...prev, ...newPagination }));
    }
  };

  // 初始加载数据
  useEffect(() => {
    if (isModalVisible) {
      if (topCategory === 'asin') {
        fetchAsinLists();
      } else if (topCategory === 'keyword') {
        fetchKeywordLists();
      } else {
        fetchHistoryRecordsWithSearch(historyPagination, operationType, searchText);
      }
    }
  }, [isModalVisible, topCategory, subCategory, operationType]);

  // 获取ASIN列表
  const fetchAsinLists = async () => {
    const pagination = getCurrentPagination();
    fetchAsinListsWithPagination(pagination);
  };

  // 添加一个新函数，接受分页参数和搜索值
  const fetchAsinListsWithSearch = async (paginationParams: PaginationState, searchValue: string) => {
    try {
      setLoading(true);
      
      // 准备请求参数
      const params: any = {
        CountryCode: countryCode, 
        PAsin: pAsin,
        CAsinType: subCategory === 'positive' ? 1 : 0,
        page: paginationParams.current,
        pagesize: paginationParams.pageSize
      };
      
      // 如果有搜索值，添加到请求参数
      if (searchValue) {
        params.CAsin = [searchValue]; // 作为数组传递
      }
      
      console.log("搜索请求参数:", params);
      
      // 调用接口获取竞品ASIN列表
      const res = await CompAsinList(params);
      
      console.log(res, "搜索结果====");
      
      if (res.data.data) {
        // 更新列表数据
        if (subCategory === 'positive') {
          setCompetitorAsins(res.data.data);
        } else {
          setNegativeAsins(res.data.data);
        }
        
        // 更新分页信息
        setCurrentPagination({
          ...paginationParams,
          total: res.data.all_count || 0,
          maxPage: res.data.max_page || 1
        });
      }
    } catch (error) {
      console.log(error, "搜索ASIN失败");
      // window.$message?.error('搜索ASIN失败');
    } finally {
      setLoading(false);
    }
  };

  // 修改原来的fetchAsinListsWithPagination函数，使用新的搜索函数
  const fetchAsinListsWithPagination = async (paginationParams: PaginationState) => {
    // 使用当前的搜索文本
    fetchAsinListsWithSearch(paginationParams, searchText);
  };

  // 获取关键词列表
  const fetchKeywordLists = async () => {
    const pagination = getCurrentPagination();
    fetchKeywordListsWithPagination(pagination);
  };

  // 获取关键词列表，带分页和搜索
  const fetchKeywordListsWithPagination = async (paginationParams: PaginationState) => {
    fetchKeywordListsWithSearch(paginationParams, searchText);
  };

  // 获取关键词列表，带分页和搜索
  const fetchKeywordListsWithSearch = async (paginationParams: PaginationState, searchValue: string) => {
    try {
      setLoading(true);
      
      // 准备请求参数
      const where: any = {
        CountryCode: countryCode,
        PAsin: pAsin,
        KeywordType: subCategory === 'positive' ? '1' : '0'
      };
      
      // 如果有搜索值，添加到请求参数
      if (searchValue) {
        where.Keyword = searchValue;
      }
      
      // 调用接口获取关键词列表
      const res = await AuthAsinShowFilteredKeyword({
        page: paginationParams.current,
        pagesize: paginationParams.pageSize,
        where
      });
      
      if (res.data && res.data.data) {
        // 更新列表数据
        if (subCategory === 'positive') {
          setPositiveKeywords(res.data.data);
        } else {
          setNegativeKeywords(res.data.data);
        }
        
        // 更新分页信息
        setCurrentPagination({
          ...paginationParams,
          total: res.data.all_count || 0,
          maxPage: res.data.max_page || 1
        });
      }
    } catch (error) {
      window.$message?.error('搜索关键词失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取历史记录
  const fetchHistoryRecords = async () => {
    const pagination = getCurrentPagination();
    fetchHistoryRecordsWithPagination(pagination);
  };

  // 获取历史记录，带分页和筛选
  const fetchHistoryRecordsWithPagination = async (paginationParams: PaginationState) => {
    fetchHistoryRecordsWithSearch(paginationParams, operationType, searchText);
  };

  // 获取历史记录，带分页、操作类型和搜索值
  const fetchHistoryRecordsWithSearch = async (
    paginationParams: PaginationState, 
    opType: string, 
    searchValue: string
  ) => {
    try {
      setLoading(true);
      
      // 准备请求参数
      const where: any = {
        CountryCode: countryCode,
        PAsin: pAsin
      };
      
      // 如果有操作类型筛选
      if (opType !== 'all') {
        where.OpType = opType;
      }
      
      // 如果有搜索值，添加到请求参数
      if (searchValue) {
        where.OpValue = searchValue;
      }
      
      // 调用接口获取历史记录
      const res = await AuthAsinShowFilteredUserShopCompetitiveLog({
        page: paginationParams.current,
        pagesize: paginationParams.pageSize,
        where
      });
      
      if (res.data && res.data.data) {
        // 更新列表数据
        setHistoryRecords(res.data.data);
        
        // 更新分页信息
        setHistoryPagination({
          ...paginationParams,
          total: res.data.all_count || 0,
          maxPage: res.data.max_page || 1
        });
      }
    } catch (error) {
      window.$message?.error('获取历史记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理添加ASIN
  const handleAddAsin = async () => {
    if (!addAsinText.trim()) {
      window.$message?.warning('请输入ASIN');
      return;
    }

    // 将文本分割为多行，进行去重
    const asinList = Array.from(new Set(
      addAsinText.split('\n')
        .map(asin => asin.trim())
        .filter(asin => asin && /^[A-Z0-9]{10}$/.test(asin))
    ));

    if (asinList.length === 0) {
      window.$message?.warning('没有有效的ASIN');
      return;
    }

    if (asinList.length > 10) {
      window.$message?.warning('一次最多添加10个ASIN');
      return;
    }

    try {
      setLoading(true);
      
      // 调用接口添加ASIN
      const res = await CompAsin({
        CountryCode: countryCode,
        PAsin: pAsin,
        CAsin: asinList,
        // 竞品词类型 1表示肯定，0表示否定
        CAsinType: subCategory === 'positive' ? 1 : 0,
        Op: 'add' // 添加操作
      });

      if (res && res.data) {
        window.$message?.success('添加成功');
        setAddAsinText('');
        setIsAddModalVisible(false);
        
        // 重新获取列表数据
        fetchAsinLists();
      } 
    } catch (error) {
      window.$message?.error('添加失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除ASIN
  const handleDeleteAsin = async (asin: string) => {
    try {
      setLoading(true);
      
      // 调用接口删除ASIN
      const res = await CompAsin({
        CountryCode: countryCode,
        PAsin: pAsin,
        CAsin: [asin], // 修改为数组格式
        CAsinType: subCategory === 'positive' ? 1 : 0, // 根据subCategory决定类型
        Op: 'remove' // 删除操作
      });

      if (res && res.data) {
        window.$message?.success('删除成功');
        
        // 重新获取列表数据
        fetchAsinLists();
      }
    } catch (error) {
      window.$message?.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除关键词
  const handleDeleteKeyword = async (id: string) => {
    try {
      setLoading(true);
      
      // 调用接口删除关键词
      const res = await AuthAsinDeleteKeyWord({
        ID: id
      });

      if (res && res.data) {
        window.$message?.success('删除成功');
        
        // 重新获取列表数据
        fetchKeywordLists();
      }
    } catch (error) {
      window.$message?.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理添加关键词
  const handleAddKeyword = async () => {
    if (!addAsinText.trim()) {
      window.$message?.warning('请输入关键词');
      return;
    }

    // 将文本分割为多行，进行去重
    const rawKeywordList = addAsinText.split('\n')
      .map(keyword => keyword.trim())
      .filter(keyword => keyword);
    
    // 检查关键词长度和特殊字符
    const invalidKeywords: { keyword: string, reason: string }[] = [];
    const validKeywords: string[] = [];
    
    // 特殊字符正则表达式
    const specialCharsRegex = /[@#*$%^&()+=[\]{}|\\<>?]/;
    
    // 上标和下标符号正则表达式
    const superSubScriptRegex = /[²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉₊₋₌₍₎ᵃᵇᶜᵈᵉᶠᵍʰⁱʲᵏˡᵐⁿᵒᵖʳˢᵗᵘᵛʷˣʸᶻ]/;
    
    // 表情符号的正则表达式
    // const emojiRegex = /[\p{Emoji}]/u;
    
    rawKeywordList.forEach(keyword => {
      if (keyword.length > 80) {
        invalidKeywords.push({ 
          keyword, 
          reason: '长度超过80个字符' 
        });
      } else if (specialCharsRegex.test(keyword)) {
        invalidKeywords.push({ 
          keyword, 
          reason: '包含特殊字符' 
        });
      } else if (superSubScriptRegex.test(keyword)) {
        invalidKeywords.push({ 
          keyword, 
          reason: '包含上标或下标符号' 
        });
      }
      //  else if (emojiRegex.test(keyword)) {
      //   invalidKeywords.push({ 
      //     keyword, 
      //     reason: '包含表情符号' 
      //   });
      // } 
      else {
        validKeywords.push(keyword);
      }
    });
    
    // 去重
    const keywordList = Array.from(new Set(validKeywords));

    // 如果有无效关键词，显示警告
    if (invalidKeywords.length > 0) {
      const invalidKeywordMessages = invalidKeywords.map(item => 
        `"${item.keyword.length > 10 ? item.keyword.substring(0, 10) + '...' : item.keyword}": ${item.reason}`
      );
      window.$notification?.warning({
        message: "以下关键词无效，",
        description: <div>{invalidKeywordMessages.map(item => <div>{item}</div>)}</div>
    });
      // window.$notification?.warning(
      //   `以下关键词无效，\n${invalidKeywordMessages.join('\n')}`
      // );
      return;
    }

    if (keywordList.length === 0) {
      window.$message?.warning('没有有效的关键词');
      return;
    }

    if (keywordList.length > 10) {
      window.$message?.warning('一次最多添加10个关键词');
      return;
    }

    try {
      setLoading(true);
      
      // 调用接口添加关键词
      const res = await AuthAsinAddKeyWord({
        CountryCode: countryCode,
        PAsin: pAsin,
        Keyword: keywordList,
        KeywordType: subCategory === 'positive' ? '1' : '0'
      });

      if (res && res.data) {
        window.$message?.success('添加成功');
        setAddAsinText('');
        setIsAddModalVisible(false);
        
        // 重新获取列表数据
        fetchKeywordLists();
      } 
    } catch (error) {
      window.$message?.error('添加失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页变化
  const handleTableChange = (newPagination: any) => {
    // 直接使用newPagination中的值调用API，而不是先更新状态
    const updatedPagination = {
      ...getCurrentPagination(),
      current: newPagination.current,
      pageSize: newPagination.pageSize
    };
    
    // 更新状态
    setCurrentPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize
    });
    
    // 根据当前分类调用不同的API
    if (topCategory === 'asin') {
      fetchAsinListsWithPagination(updatedPagination);
    } else if (topCategory === 'keyword') {
      fetchKeywordListsWithPagination(updatedPagination);
    } else {
      fetchHistoryRecordsWithPagination(updatedPagination);
    }
  };

  // 获取当前数据
  const getCurrentData = () => {
    if (topCategory === 'asin') {
      return subCategory === 'positive' ? competitorAsins : negativeAsins;
    } else if (topCategory === 'keyword') {
      return subCategory === 'positive' ? positiveKeywords : negativeKeywords;
    } else {
      return historyRecords;
    }
  };

  // 修改搜索处理函数
  const handleSearch = (value: string) => {
    setSearchText(value);
    
    // 创建一个新的分页对象，重置为第一页
    const searchPagination = {
      ...getCurrentPagination(),
      current: 1
    };
    
    // 更新状态
    setCurrentPagination({ current: 1 });
    
    // 根据当前分类调用不同的API
    if (topCategory === 'asin') {
      fetchAsinListsWithSearch(searchPagination, value);
    } else if (topCategory === 'keyword') {
      fetchKeywordListsWithSearch(searchPagination, value);
    } else {
      // 直接调用接口，传递新值
      fetchHistoryRecordsWithSearch({
        ...historyPagination,
        current: 1
      }, operationType, value);
    }
  };

  // 处理顶级分类切换
  const handleTopCategoryChange = (key: string) => {
    setTopCategory(key);
    setSearchText(''); // 清空搜索框
    
    if (key === 'history') {
      setSubCategory('');
    } else {
      setSubCategory('positive');
    }
  };

  // 处理子分类切换
  const handleSubCategoryChange = (key: string) => {
    setSubCategory(key);
    setSearchText(''); // 清空搜索框
  };

  // 处理历史记录筛选变化
  const handleHistoryFilterChange = (opType?: string, searchValue?: string) => {
    // 使用传入的参数或当前状态值
    const opTypeToUse = opType !== undefined ? opType : operationType;
    const searchValueToUse = searchValue !== undefined ? searchValue : searchText;
    
    // 直接使用参数调用接口
    fetchHistoryRecordsWithSearch({
      ...historyPagination,
      current: 1
    }, opTypeToUse, searchValueToUse);
  };

  // 获取当前分类的标题
  const getCategoryTitle = () => {
    if (topCategory === 'history') {
      return '历史记录';
    }
    const topCat = topCategories.find(cat => cat.key === topCategory);
    const subCat = topCat?.children.find(child => child.key === subCategory);
    // return `${topCat?.label} - ${subCat?.label}`;
    return `${subCat?.label}`;
  };

  // 渲染历史记录的筛选器
  const renderHistoryFilters = () => {
    if (topCategory !== 'history') return null;
    
    return (
      <ASpace>
        <ASelect
          style={{ width: 120 }}
          value={operationType}
          onChange={(value) => {
            setOperationType(value);
            // 直接调用接口，传递新值
            handleHistoryFilterChange(value, searchText);
          }}
          options={operationTypes}
          placeholder="操作类型"
        />
        <Input
          placeholder="搜索ASIN/关键词"
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          onPressEnter={() => handleHistoryFilterChange(operationType, searchText)}
          style={{ width: 200 }}
          suffix={
            <p onClick={() => handleHistoryFilterChange(operationType, searchText)}>
              <Icon icon="mdi:magnify" />
            </p>
          }
          disabled={loading}
          allowClear
          onClear={() => {
            setSearchText('');
            handleHistoryFilterChange(operationType, '');
          }}
        />
      </ASpace>
    );
  };

  // 渲染分类标签页
  const renderCategoryTabs = () => {
    if (topCategory === 'history') return null;

    const currentTopCategory = topCategories.find(cat => cat.key === topCategory);
    if (!currentTopCategory) return null;

    return (
      <ASegmented
        value={subCategory}
        onChange={(key) => handleSubCategoryChange(key as string)}
        options={currentTopCategory.children.map(child => ({
          label: child.label,
          value: child.key
        }))}
        style={{ marginBottom: 16 }}
      />
    );
  };

  // 获取表格列定义
  const getColumns = () => {
    if (topCategory === 'asin') {
      return [
        {
          title: 'ASIN',
          dataIndex: 'CAsin',
          key: 'CAsin',
          align: 'center' as const
        },
        {
          title: '添加时间',
          dataIndex: 'AddDatetime',
          key: 'AddDatetime',
          align: 'center' as const
        },
        {
          title: '添加账户',
          dataIndex: 'OpName',
          key: 'OpName',
          align: 'center' as const
        },
        {
          title: '操作',
          key: 'action',
          align: 'center' as const,
          render: (_: any, record: any) => (
            record.ProcessState === 0 ? (
              <ATag color='yellow'>处理中</ATag>
            ) : (
              <APopconfirm
                title="确定删除此ASIN吗?"
                onConfirm={() => handleDeleteAsin(record.CAsin)}
                okText="确定"
                cancelText="取消"
                disabled={loading}
              >
                <AButton 
                  type="text" 
                  danger 
                  icon={<Icon icon="mdi:trash-can-outline" style={{ fontSize: '18px' }} />}
                  loading={loading}
                  disabled={loading}
                  style={{ padding: '4px 8px' }}
                />
              </APopconfirm>
            )
          )
        }
      ];
    } else if (topCategory === 'keyword') {
      return [
        {
          title: '关键词',
          dataIndex: 'Keyword',
          key: 'Keyword',
          align: 'center' as const
        },
        {
          title: '添加时间',
          dataIndex: 'AddDatetime',
          key: 'AddDatetime',
          align: 'center' as const,
          render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
        },
        {
          title: '添加账户',
          dataIndex: 'OpName',
          key: 'OpName',
          align: 'center' as const
        },
        {
          title: '操作',
          key: 'action',
          align: 'center' as const,
          render: (_: any, record: any) => (
            record.ProcessState === 0 ? (
              <ATag color='yellow'>处理中</ATag>
            ) : (
            <APopconfirm
              title="确定删除此关键词吗?"
              onConfirm={() => handleDeleteKeyword(record.ID)}
              okText="确定"
              cancelText="取消"
              disabled={loading}
            >
              <AButton 
                type="text" 
                danger 
                icon={<Icon icon="mdi:trash-can-outline" style={{ fontSize: '18px' }} />}
                loading={loading}
                disabled={loading}
                style={{ padding: '4px 8px' }}
              />
            </APopconfirm>
            )
          )
        }
      ];
    } else {
      return [
        {
          title: 'ASIN/关键词',
          dataIndex: 'OpValue',
          key: 'OpValue',
          align: 'center' as const
        },
        {
          title: '操作类型',
          dataIndex: 'OpContent',
          key: 'OpContent',
          align: 'center' as const,
          render: (text: string) => (
            <ATag color={text.includes("添加") ? 'green' : 'red'}>
              {text}
            </ATag>
          )
        },
        {
          title: '操作时间',
          dataIndex: 'OpDatetime',
          key: 'OpDatetime',
          align: 'center' as const,
          render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
        },
        {
          title: '操作账户',
          dataIndex: 'OpName',
          key: 'OpName',
          align: 'center' as const
        }
      ];
    }
  };

  // 处理添加按钮点击
  const handleAddButtonClick = () => {
    if (!loading) {
      setAddAsinText('');
      setIsAddModalVisible(true);
    }
  };

  // 处理添加提交
  const handleAddSubmit = () => {
    if (topCategory === 'asin') {
      handleAddAsin();
    } else if (topCategory === 'keyword') {
      handleAddKeyword();
    }
  };

  // 获取添加弹窗标题
  const getAddModalTitle = () => {
    if (topCategory === 'asin') {
      return `添加${subCategory === 'positive' ? '竞品' : '否定竞品'}ASIN`;
    } else {
      return `添加${subCategory === 'positive' ? '投放' : '否定'}关键词`;
    }
  };

  // 获取当前分页
  const pagination = getCurrentPagination();

  const modalTitle = (
    <div style={guideStyles.modalTitle}>
      <Icon icon="mingcute:ai-fill" style={guideStyles.aiIcon} />
      <span>{pAsin} 竞品策略配置</span>
    </div>
  );

  return (
    <>
      {/* 主按钮 */}
      <AButton 
         type="link"
        onClick={() => setIsModalVisible(true)}
        size="small"
      >
       <div className='flex items-center'>
       <Icon icon="mingcute:ai-fill" />
       竞品策略
       </div>
      </AButton>

      {/* 主弹窗 */}
      <AModal
        title={modalTitle}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        maskClosable={false}
        width={1000}
        footer={null}
      >
        <ATabs
          activeKey={topCategory}
          onChange={handleTopCategoryChange}
          items={topCategories.map(cat => ({
            key: cat.key,
            label: cat.label
          }))}
        />

        {renderCategoryTabs()}

        <ACard
          title={getCategoryTitle()}
          extra={
            topCategory !== 'history' ? (
              <ASpace>
                <Input
                  placeholder={`搜索${topCategory === 'asin' ? 'ASIN' : '关键词'}`}
                  value={searchText}
                  onChange={e => setSearchText(e.target.value)}
                  onPressEnter={() => handleSearch(searchText)}
                  style={{ width: 200 }}
                  suffix={
                    <p onClick={() => handleSearch(searchText)}>
                      <Icon icon="mdi:magnify" />
                    </p>
                  }
                  disabled={loading}
                  allowClear
                  onClear={() => {
                    setSearchText('');
                    handleSearch('');
                  }}
                />
                <AButton
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddButtonClick}
                  disabled={loading}
                >
                  添加{topCategory === 'asin' ? 'ASIN' : '关键词'}
                </AButton>
              </ASpace>
            ) : (
              renderHistoryFilters()
            )
          }
        >
          <ATable
            columns={getColumns()}
            dataSource={getCurrentData()}
            rowKey={topCategory === 'asin' ? 'CAsin' : (topCategory === 'keyword' ? 'id' : 'op_id')}
            loading={loading}
            size="small"
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              pageSizeOptions: ['20', '50', '100'],
              showTotal: (total) => `共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ y: 500 }}
            locale={{
              emptyText: <Empty description={`暂无${getCategoryTitle()}`} />
            }}
          />
        </ACard>
      </AModal>

      {/* 添加ASIN/关键词弹窗 */}
      <AModal
        title={
          <div style={guideStyles.modalTitle}>
            <span>{getAddModalTitle()}</span>
          </div>
        }
        open={isAddModalVisible}
        onCancel={() => setIsAddModalVisible(false)}
        maskClosable={false}
        closeIcon={!loading && undefined}
        onOk={handleAddSubmit}
        okButtonProps={{ disabled: loading, loading: loading }}
        cancelButtonProps={{ disabled: loading }}
        confirmLoading={loading}
        width={700}
      >
        <TextArea
          rows={6}
          value={addAsinText}
          onChange={e => setAddAsinText(e.target.value)}
          placeholder={`在这里输入${topCategory === 'asin' ? 'ASIN' : '关键词'}，每行一个，一次可以批量添加10个，可以多次添加`}
          disabled={loading}
        />
        <div style={guideStyles.guideBox}>
          {topCategory === 'asin' ? (
            <>
              <div style={guideStyles.title}>
                <Icon icon="hugeicons:ai-idea" style={guideStyles.icon} />
                <span>{subCategory === 'positive' ? '竞品' : '否定'}ASIN操作指南</span>
              </div>
              {subCategory === 'positive' ? (
                <div style={guideStyles.bulletPoint}>
                  <Text style={{ flex: 1 }}>
                    AI系统具有自动识别和添加竞品ASIN的功能，手动添加竞品ASIN将作为竞品探索的补充，以提升广告效果
                  </Text>
                </div>
              ) : (
                <NegativeAsinGuide />
              )}
            </>
          ) : (
            <>
              <div style={guideStyles.title}>
                <Icon icon="hugeicons:ai-idea" style={guideStyles.icon} />
                <span>{subCategory === 'positive' ? '投放' : '否定'}关键词操作指南</span>
              </div>
              {subCategory === 'positive' ? (
                <PositiveKeywordGuide />
              ) : (
                <NegativeKeywordGuide />
              )}
            </>
          )}
        </div>
      </AModal>
    </>
  );
};

export default CompetitorStrategyConfig;