import DailySonDetails from '@/pages/ai/daily/modules/DailySonDetails';

// 数据对比ASIN详情
export function Component() {
  const { t } = useTranslation();
  const { Content } = ALayout;
  const [searchParams] = useSearchParams();

  const Type = searchParams.get('type');
  const CountryCode = searchParams.get('countryCode');
  const Asin = searchParams.get('asin');

  return (
    <div className="h-full w-full flex flex-col">
      <ALayout className="h-full">
        <Content>
          <DailySonDetails
            Type={Type}
            CountryCode={CountryCode}
            Asin={Asin}
          />
        </Content>
      </ALayout>
    </div>
  );
}
