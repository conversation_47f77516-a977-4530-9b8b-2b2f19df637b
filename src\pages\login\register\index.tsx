import { Button, Form, Input, Space } from 'antd';
import { useLocation } from 'react-router-dom';
import { useLogin } from '@/hooks/common/login';
import CaptchaQRCode from '../captch-qr-code';
interface FormModel {
  companyName: string;
  phone: string;
  code: string;
  password: string;
  confirmPassword: string;
}

export const Component = () => {
  const { t } = useTranslation();
  const { label, isCounting, loading, getCaptcha } = useCaptcha();
  const { toggleLoginModule } = useRouterPush();
  const [form] = Form.useForm<FormModel>();
  const { formRules } = useFormRules();
  const captchaRef = useRef<any>(null);
  const [captchaKey, setCaptchaKey] = useState('');
  const { loading: toLoginLoading, toLogin } = useLogin();
  const registerModalRef = useRef<any>(null);

  const location = useLocation(); // 获取 location 对象
  const [LinkCode, setLinkCode] = useState<string | null>(null);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const code = queryParams.get('linkcode');
    const phone = queryParams.get('phone');

    if (code) {
      setLinkCode(code);
      form.setFieldsValue({ LinkCode: code });
    }
    if (phone) {
      form.setFieldsValue({ phone });
    }
  }, [location.search, form]);

  const getCaptchaCode = () => {
    form.validateFields(['phone', 'captchaValue']).then(() => {
      const params = form.getFieldsValue();
      getCaptcha(form, { ...params, captchaKey }, (data: any) => {
        captchaRef.current.refreshCaptcha();
      }); // 在确认后调用 getCaptcha
    });
  };
  async function handleSubmit() {
    const params = await form.validateFields();
    // console.log(params);
    // return
    toLogin({ ...params, lang: 'zh', register: true }, captchaRef);
    // request to reset password
    // window.$message?.success(t('page.login.common.validateSuccess'));
  }

  useKeyPress('enter', () => {
    handleSubmit();
  });

  return (
    <>
      <h3 className="text-18px text-primary font-medium">{t('page.login.register.title')}</h3>
      <RegisterModel
        ref={registerModalRef}
        onAgree={() => {
          setTimeout(() => {
            form.setFieldsValue({ agree: true });
            // console.log('Form values:', form.getFieldsValue());
            form.validateFields(['agree']);
          }, 0);
          registerModalRef.current?.close();
        }}
      />
      <Form
        form={form}
        className="pt-24px"
      >
        {/* 公司名称 */}
        <Form.Item
          name="companyName"
          rules={formRules.companyName}
        >
          <Input placeholder={t('page.login.common.companyNamePlaceholder')}></Input>
        </Form.Item>
        <Form.Item
          name="phone"
          rules={formRules.phone}
        >
          <Input placeholder={t('page.login.common.phonePlaceholder')}></Input>
        </Form.Item>
        <CaptchaQRCode
          ref={captchaRef}
          onCaptchaChange={(value, key) => {
            setCaptchaKey(key);
          }}
        />
        <Form.Item
          name="code"
          rules={formRules.phoneCode}
        >
          <div className="w-full flex-y-center gap-16px">
            <Input placeholder={t('page.login.common.codePlaceholder')} />
            <Button
              // size="large"
              disabled={isCounting}
              loading={loading}
              onClick={getCaptchaCode}
            >
              {label}
            </Button>
          </div>
        </Form.Item>
        <Form.Item
          rules={formRules.pwd}
          name="password"
        >
          {/* 最少8位 包含大小写字母、数字、特殊符号 */}
          <Input placeholder={'请输入最短8位，包含大小写字母、数字的密码'}></Input>
        </Form.Item>
        {/* LinkCode  */}
        {!LinkCode ? null : (
          <Form.Item
            name="LinkCode"
            label="邀请码"
            rules={[
              {
                pattern: /^[a-zA-Z0-9]{6}$/, // Corrected pattern without quotes
                message: '请输入正确的邀请码'
              }
            ]}
          >
            <Input
              placeholder="请输入邀请码(非必填)"
              disabled={Boolean(LinkCode)}
            />
          </Form.Item>
        )}
        {/* 同意协议 */}
        <Form.Item
          // rules={}
          name="agree"
          key="agree"
          valuePropName="checked"
          rules={[
            {
              validator: (_, value) => (value ? Promise.resolve() : Promise.reject(new Error('请勾选协议')))
            }
          ]}
        >
          {/* <div className='flex items-center gap-2px'> */}
          <ACheckbox
          // checked={form.getFieldValue('agree')}
          //  onChange={(e) => form.setFieldsValue({ agree: e.target.checked })}
          >
            <div className="flex items-center gap-4px">
              阅读并同意
              <p
                className="cursor-pointer text-[#2F54EB]"
                onClick={e => {
                  e.stopPropagation();
                  registerModalRef.current?.open();
                }}
              >
                ATLAS软件条款和条件
              </p>
            </div>
            {/* </div> */}
          </ACheckbox>
        </Form.Item>
        <Space
          direction="vertical"
          className="w-full"
          size={18}
        >
          <Button
            type="primary"
            size="large"
            shape="round"
            block
            loading={toLoginLoading}
            onClick={handleSubmit}
          >
            {t('common.confirm')}
          </Button>

          <Button
            size="large"
            shape="round"
            block
            onClick={() => toggleLoginModule('pwd-login')}
          >
            {t('page.login.common.back')}
          </Button>
        </Space>
      </Form>
    </>
  );
};

Component.displayName = 'Register';
