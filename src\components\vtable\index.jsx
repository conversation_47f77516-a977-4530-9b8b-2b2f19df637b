import { Form, Radio, Table, Tabs, notification } from 'antd';
// import { QuestionCircleOutlined } from '@ant-design/icons';
import BigNumber from 'bignumber.js';

import { getSummarizeDataInfoOneCountry } from '@/service/api';
import { useExportPDF } from '@/utils/useExportPDF';
import { selectUserInfo } from '@/store/slice/auth';

const UserSearch = lazy(() => import('@/pages/ai/daily/modules/UserSearch'));
const CurrencyUnit = lazy(() => import('@/pages/ai/daily/modules/CurrencyUnit'));

const SimpleTable = ({ data }) => {
  const { t } = useTranslation();
  const [Loading, setLoading] = useState(false);
  const [api, contextHolder] = notification.useNotification();
  // 存储表格数据
  const [tableData, setTableData] = useState([]);
  const [summaryRow, setSummaryRow] = useState(null);
  const { tableWrapperRef, scrollConfig } = useTableScroll();
  const tableRef = useRef(null);
  const { exportPDF } = useExportPDF();
  const [exporting, setExporting] = useState(false);
  const [form] = Form.useForm();
  const countryCode = form.getFieldValue('country');
  const nav = useNavigate();
  // 获取用户信息
  const userInfo = useAppSelector(selectUserInfo);
  // 添加时间周期状态
  const [DateType, setDateType] = useState(() => {
    if (!userInfo?.active_shop_regdatetime) return 'days';

    const regDate = new Date(userInfo.active_shop_regdatetime);
    const currentDate = new Date();
    const diffInDays = Math.floor((currentDate - regDate) / (1000 * 60 * 60 * 24));

    return diffInDays <= 30 ? 'days' : 'week';
  });
  // 添加托管状态切换
  const [activeTab, setActiveTab] = useState('current'); // 默认显示当前托管数据

  const handleExport = () => {
    if (tableRef.current) {
      setExporting(true);
      exportPDF('ExportedTable', tableRef.current);
    }
  };

  // 处理from数据
  const handleFormData = () => {
    const values = form.getFieldsValue();
    let manageState;

    if (activeTab === 'canceled') {
      manageState = '3';
    } else if (values.adStatus === 'undefined') {
      manageState = undefined;
    } else {
      manageState = values.adStatus;
    }

    return {
      UID: values.active_shop_id,
      CountryCode: values.country,
      StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined,
      ManageState: manageState,
      // 去除空格
      ParentAsin: values.parentAsin?.replace(/\s/g, '')
    };
  };

  // 处理请求数据并设置ManageState
  const prepareRequestData = (params, currentTab) => {
    // 如果有ParentAsin 把键改为P_Asin
    const req_data = {
      ...params,
      ...(params.ParentAsin ? { P_Asin: params.ParentAsin } : {}),
      DateType: params.DateType || DateType
    };

    // 始终根据当前实际Tab状态设置ManageState，无论是Tab切换还是筛选
    if (currentTab === 'canceled') {
      req_data.ManageState = '3';
      console.log('设置为取消托管查询参数:', req_data);
    } else if (params.ManageState !== undefined && params.ManageState !== null) {
      req_data.ManageState = params.ManageState;
    } else {
      // 如果是当前托管且没有ManageState值，就不传递此参数
      delete req_data.ManageState;
    }

    return req_data;
  };

  // 获取表格数据
  const getTableData = async params => {
    console.log(params, 'form');
    setLoading(true);
    try {
      // 提取并删除临时参数
      const currentTab = params._currentTab || activeTab;
      if (params._currentTab) {
        delete params._currentTab;
      }

      // 准备请求数据
      const req_data = prepareRequestData(params, currentTab);

      console.log('发送请求参数:', req_data, '当前实际Tab:', currentTab); // 添加日志以检查请求参数

      const res = await getSummarizeDataInfoOneCountry(req_data);
      if (res && res.data && res.data.data.length > 0) {
        // 遍历res.data.data 如果有null 或者None 则赋值0
        const updatedData = res.data.data.map(item => {
          const newItem = { ...item };
          for (const key in newItem) {
            if (newItem[key] === null || newItem[key] === 'None') {
              newItem[key] = '-';
            }
          }
          return newItem;
        });

        // Calculate summary row
        const summary = {};
        const sumFields = [
          '广告总销售额',
          '广告总花费',
          'DeepBI广告销售额',
          'DeepBI广告花费',
          '原计划广告销售额',
          '原计划广告花费',
          '总销售额',
          '自然销售额'
        ];

        sumFields.forEach(field => {
          summary[field] = updatedData.reduce((sum, item) => {
            // 如果为- 则返回0
            if (item[field] === '-' || item[field] === 'None') {
              item[field] = 0.0;
            }
            // 保留两位小数
            return new BigNumber(sum).plus(new BigNumber(item[field] || 0)).toFixed(2);
          }, '0.00');
        });

        // Calculate ACOS and other ratios
        summary['广告总ACOS'] =
          summary['广告总销售额'] === '0.00' || summary['广告总销售额'] === '0'
            ? '-'
            : `${new BigNumber(summary['广告总花费'])
                .dividedBy(new BigNumber(summary['广告总销售额']))
                .times(100)
                .toFixed(2)}%`;

        summary['DeepBI广告总ACOS'] =
          summary['DeepBI广告销售额'] === '0.00' || summary['DeepBI广告销售额'] === '0'
            ? '-'
            : `${new BigNumber(summary['DeepBI广告花费'])
                .dividedBy(new BigNumber(summary['DeepBI广告销售额']))
                .times(100)
                .toFixed(2)}%`;

        summary['原计划广告总ACOS'] =
          summary['原计划广告销售额'] === '0.00' || summary['原计划广告销售额'] === '0'
            ? '-'
            : `${new BigNumber(summary['原计划广告花费'])
                .dividedBy(new BigNumber(summary['原计划广告销售额']))
                .times(100)
                .toFixed(2)}%`;

        summary['自然比例'] =
          summary['总销售额'] === '0.00' || summary['总销售额'] === '0'
            ? '-'
            : `${new BigNumber(summary['自然销售额'])
                .dividedBy(new BigNumber(summary['总销售额']))
                .times(100)
                .toFixed(2)}%`;

        // TACOS 花费/销售额
        summary.TACOS =
          summary['总销售额'] === '0.00' || summary['总销售额'] === '0'
            ? '-'
            : `${new BigNumber(summary['广告总花费'])
                .dividedBy(new BigNumber(summary['总销售额']))
                .times(100)
                .toFixed(2)}%`;

        // Add a label for the summary row
        summary['国家'] = t('page.daily.summary');
        summary['总销售日期'] = t('page.daily.summary');

        setTableData(updatedData);
        setSummaryRow(summary);
      } else {
        console.log('没有数据');
        setTableData([]);
        setSummaryRow(null);
      }
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('获取数据失败:', error);
      setLoading(false);
      setTableData([]);
      setSummaryRow(null);
    }
  };

  // 处理Tab切换
  const handleTabChange = key => {
    // 加载状态下不允许切换Tab
    if (Loading) return;

    // 注意：这里设置activeTab是异步的，不会立即生效
    setActiveTab(key);

    // 重要：使用传入的key参数（新的Tab值）而不是依赖activeTab状态
    console.log('Tab切换到:', key);

    // 切换Tab后重新获取数据
    const values = form.getFieldsValue();
    let manageState;

    // 根据传入的key参数（而非activeTab）设置正确的ManageState
    if (key === 'canceled') {
      manageState = '3';
    } else {
      manageState = undefined;
    }

    getTableData({
      UID: values.active_shop_id,
      CountryCode: values.country,
      StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined,
      ManageState: manageState,
      ParentAsin: values.parentAsin?.replace(/\s/g, ''),
      DateType,
      // 添加一个临时参数，用于在getTableData中识别当前实际的Tab
      _currentTab: key
    });
  };

  // 处理ASIN数据类型切换
  const handleAsinDataTypeChange = type => {
    if (type === 'current') {
      nav(`/ai/daily-hosted`);
    } else {
      nav(`/ai/daily-nhosted`);
    }
  };

  // 处理时间周期切换
  const handleDateTypeChange = e => {
    const period = e.target.value;
    setDateType(period);

    // 重新获取数据
    const values = handleFormData();

    // 确保根据当前Tab设置正确的ManageState
    if (activeTab === 'canceled') {
      values.ManageState = '3';
    }

    // 传递当前Tab状态
    getTableData({
      ...values,
      DateType: period,
      _currentTab: activeTab
    });
  };

  useEffect(() => {
    if (exporting) {
      notification.info({
        message: '导出PDF',
        description: '导出中...',
        placement: 'topRight'
      });
      setExporting(false);
    }
  }, [exporting, api]);

  // 处理搜索请求，确保考虑当前Tab状态
  const handleSearch = values => {
    // 根据当前Tab状态处理ManageState
    if (activeTab === 'canceled') {
      values.ManageState = '3';
    }

    // 传递当前Tab状态
    getTableData({
      ...values,
      _currentTab: activeTab
    });
  };

  // 处理刷新页面操作
  const handleRefresh = () => {
    // 获取表单数据并传递当前Tab状态
    const values = handleFormData();
    getTableData({
      ...values,
      _currentTab: activeTab
    });
  };

  const columns = [
    // {
    //   title: '国家',
    //   dataIndex: '国家',
    //   key: 'country',
    //   width: 100,
    //   fixed: 'left'
    // },
    {
      title: t('page.daily.columns.date'),
      dataIndex: '总销售日期',
      key: 'totalSaleDate',
      width: 100,
      fixed: 'left'
      // sorter: (a, b) => new Date(a.总销售日期) - new Date(b.总销售日期)
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.adTotalSales')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.adTotalSales')}</div>
          </APopover>
        );
      },
      dataIndex: '广告总销售额',
      key: 'adTotalSales',
      width: 100,
      align: 'center',
      // sorter: (a, b) => Number(a.广告总销售额 || 0) - Number(b.广告总销售额 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.adTotalSpend')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.adTotalSpend')}</div>
          </APopover>
        );
      },
      dataIndex: '广告总花费',
      key: 'adTotalSpend',
      width: 100,
      align: 'center',
      // sorter: (a, b) => Number(a.广告总花费 || 0) - Number(b.广告总花费 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.adTotalAcos')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.adTotalAcos')}</div>
          </APopover>
        );
      },
      dataIndex: '广告总ACOS',
      key: 'adTotalAcos',
      width: 100,
      align: 'center',
      // sorter: (a, b) => {
      //   const aValue = parseFloat(String(a.广告总ACOS).replace('%', '') || 0);
      //   const bValue = parseFloat(String(b.广告总ACOS).replace('%', '') || 0);
      //   return aValue - bValue;
      // },
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.aiAdSales')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.aiAdSales')}</div>
          </APopover>
        );
      },
      dataIndex: 'DeepBI广告销售额',
      key: 'aiAdSales',
      width: 100,
      align: 'center',
      // sorter: (a, b) => Number(a.DeepBI广告销售额 || 0) - Number(b.DeepBI广告销售额 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.aiAdSpend')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.aiAdSpend')}</div>
          </APopover>
        );
      },
      dataIndex: 'DeepBI广告花费',
      key: 'aiAdSpend',
      width: 100,
      align: 'center',
      // sorter: (a, b) => Number(a.DeepBI广告花费 || 0) - Number(b.DeepBI广告花费 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.aiAdTotalAcos')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.aiAdTotalAcos')}</div>
          </APopover>
        );
      },
      dataIndex: 'DeepBI广告总ACOS',
      key: 'aiAdTotalAcos',
      width: 120,
      align: 'center',
      // sorter: (a, b) => {
      //   const aValue = parseFloat(String(a.DeepBI广告总ACOS).replace('%', '') || 0);
      //   const bValue = parseFloat(String(b.DeepBI广告总ACOS).replace('%', '') || 0);
      //   return aValue - bValue;
      // },
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.originalPlanSales')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">
              {t('page.daily.columns.originalPlanSales')}
            </div>
          </APopover>
        );
      },
      dataIndex: '原计划广告销售额',
      key: 'originalPlanSales',
      width: 130,
      align: 'center',
      // sorter: (a, b) => Number(a.原计划广告销售额 || 0) - Number(b.原计划广告销售额 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.originalPlanSpend')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">
              {t('page.daily.columns.originalPlanSpend')}
            </div>
          </APopover>
        );
      },
      dataIndex: '原计划广告花费',
      key: 'originalPlanSpend',
      width: 120,
      align: 'center',
      // sorter: (a, b) => Number(a.原计划广告花费 || 0) - Number(b.原计划广告花费 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.originalPlanAcos')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">
              {t('page.daily.columns.originalPlanAcos')}
            </div>
          </APopover>
        );
      },
      dataIndex: '原计划广告总ACOS',
      key: 'originalPlanAcos',
      width: 140,
      align: 'center',
      // sorter: (a, b) => {
      //   const aValue = parseFloat(String(a.原计划广告总ACOS).replace('%', '') || 0);
      //   const bValue = parseFloat(String(b.原计划广告总ACOS).replace('%', '') || 0);
      //   return aValue - bValue;
      // },
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.totalSales')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.totalSales')}</div>
          </APopover>
        );
      },
      dataIndex: '总销售额',
      key: 'totalSales',
      width: 100,
      align: 'center',
      // sorter: (a, b) => Number(a.总销售额 || 0) - Number(b.总销售额 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.naturalSales')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.naturalSales')}</div>
          </APopover>
        );
      },
      dataIndex: '自然销售额',
      key: 'naturalSales',
      width: 100,
      align: 'center',
      // sorter: (a, b) => Number(a.自然销售额 || 0) - Number(b.自然销售额 || 0),
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.naturalRatio')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.naturalRatio')}</div>
          </APopover>
        );
      },
      dataIndex: '自然比例',
      key: 'naturalRatio',
      width: 80,
      align: 'center',
      // sorter: (a, b) => {
      //   const aValue = parseFloat(String(a.自然比例).replace('%', '') || 0);
      //   const bValue = parseFloat(String(b.自然比例).replace('%', '') || 0);
      //   return aValue - bValue;
      // },
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    },
    {
      title: () => {
        return (
          <APopover
            content={t('page.daily.tooltips.tacos')}
            trigger="hover"
          >
            <div className="flex items-center justify-center text-[12px]">{t('page.daily.columns.tacos')}</div>
          </APopover>
        );
      },
      dataIndex: 'TACOS',
      key: 'TACOS',
      width: 80,
      align: 'center',
      // sorter: (a, b) => {
      //   const aValue = parseFloat(String(a.TACOS).replace('%', '') || 0);
      //   const bValue = parseFloat(String(b.TACOS).replace('%', '') || 0);
      //   return aValue - bValue;
      // },
      render: text => (
        <CurrencyUnit
          value={text}
          countryCode={countryCode}
        />
      )
    }
  ];

  const tableHeight = scrollConfig.y;

  return (
    <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      {contextHolder}
      <div className="sticky top-0 z-10 w-full">
        <ACard className="flex items-center justify-between">
          <UserSearch
            search={handleSearch}
            reset={() => {}}
            form={form}
            loading={Loading}
          />
        </ACard>
      </div>

      <ACard
        title={
          <div className="flex items-center justify-between">
            <div className="">
              <Tabs
                defaultActiveKey="current"
                activeKey={activeTab}
                onChange={handleTabChange}
                items={[
                  {
                    key: 'current',
                    label: t('page.daily.tabs.current'),
                    disabled: Loading
                  },
                  {
                    key: 'canceled',
                    label: t('page.daily.tabs.canceled'),
                    disabled: Loading
                  }
                ]}
              />
              {/* <ATooltip title="AI托管Listing的广告表现数据，而非全店数据,您也可搜索单个父ASIN数据">
                <QuestionCircleOutlined className="ml-1" />
              </ATooltip> */}
            </div>
            <div className="flex items-center">
              <div className="flex items-center justify-between">
                <div className="mr-2 flex space-x-2">
                  {activeTab === 'current' && (
                    <AButton
                      type={'default'}
                      onClick={() => handleAsinDataTypeChange('current')}
                    >
                      {t('page.daily.buttons.currentAsin')}
                    </AButton>
                  )}
                  {activeTab === 'canceled' && (
                    <AButton
                      type={'default'}
                      onClick={() => handleAsinDataTypeChange('cancelled')}
                    >
                      {t('page.daily.buttons.canceledAsin')}
                    </AButton>
                  )}
                </div>

                <Radio.Group
                  disabled={Loading}
                  value={DateType}
                  onChange={handleDateTypeChange}
                  buttonStyle="solid"
                >
                  <Radio.Button value="days">{t('page.daily.dateType.day')}</Radio.Button>
                  <Radio.Button value="week">{t('page.daily.dateType.week')}</Radio.Button>
                </Radio.Group>
              </div>
            </div>
          </div>
        }
        className="flex-col-stretch sm:flex-1-hidden card-wrapper"
        ref={tableWrapperRef}
      >
        <div
          className="relative"
          style={{ minHeight: tableHeight }}
          ref={tableRef}
        >
          <Table
            columns={columns}
            dataSource={tableData}
            loading={Loading}
            size="small"
            rowKey={record => record.国家 + record.总销售日期}
            pagination={false}
            scroll={{ x: 'max-content', y: tableHeight }}
            summary={() => {
              return summaryRow ? (
                <Table.Summary fixed="bottom">
                  <Table.Summary.Row>
                    {columns.map((column, index) => {
                      const value = summaryRow[column.dataIndex];
                      return (
                        <Table.Summary.Cell
                          key={index}
                          index={index}
                          align="center"
                        >
                          {value !== undefined ? (
                            <CurrencyUnit
                              className="text-primary"
                              value={value}
                              countryCode={countryCode}
                            />
                          ) : index === 0 ? (
                            t('page.daily.summary')
                          ) : (
                            '-'
                          )}
                        </Table.Summary.Cell>
                      );
                    })}
                  </Table.Summary.Row>
                </Table.Summary>
              ) : null;
            }}
            locale={{
              emptyText: (
                <AEmpty
                  image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    !Loading && form.getFieldValue('country') ? (
                      <div className="flex-col items-center">
                        <AButton
                          type="primary"
                          className="my-2"
                          onClick={handleRefresh}
                        >
                          {t('page.daily.buttons.refresh')}
                        </AButton>
                      </div>
                    ) : null
                  }
                ></AEmpty>
              )
            }}
          />
        </div>
      </ACard>
    </div>
  );
};

export default SimpleTable;
