import { Col, Form, Input, Row, Select } from 'antd';
import type { FC } from 'react';
import { memo } from 'react';
import { Icon } from '@iconify/react';

interface Props {
  search: (params: any) => void;
  authLoading: boolean;
}

const LocalSearch: FC<Props> = memo(({ search, authLoading = true }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      Asin: values.Asin,
      IsAuth: values.IsAuth,
      localSearch: 1
    };
    search(params);
  };

  return (
    <>
      <Form
        disabled={authLoading}
        form={form}
      >
        <Row
          gutter={[16, 16]}
          wrap
        >
          <Col
            span={24}
            md={12}
            lg={6}
          >
            <Form.Item
              className="m-0"
              name="Asin"
              label={t('page.listingall.search.parentAsin')}
            >
              <Input
                placeholder={t('page.listingall.search.inputParentAsin')}
                allowClear
                onPressEnter={handleSearch}
                onClear={handleSearch} // Ensure this triggers handleSearch
                suffix={
                  <p onClick={handleSearch}>
                    <Icon icon="mdi:magnify" />
                  </p>
                }
              />
            </Form.Item>
          </Col>

          <Col
            span={24}
            md={12}
            lg={7}
          >
            <Form.Item
              className="m-0"
              name="IsAuth"
              label={t('page.listingall.search.hostingStatus')}
            >
              <Select
                placeholder={t('page.listingall.search.selectHostingStatus')}
                onChange={handleSearch}
                options={[
                  { label: t('page.listingall.search.all'), value: 'undefined' },
                  { label: t('page.listingall.search.hosted'), value: '1' },
                  { label: t('page.listingall.search.unhosted'), value: '0' }
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
});

export default LocalSearch;
