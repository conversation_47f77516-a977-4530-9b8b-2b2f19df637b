const route: App.I18n.Schema['translation']['route'] = {
  login: '登录',
  403: '无权限',
  404: '页面不存在',
  500: '服务器错误',
  'iframe-page': '外链页面',
  home: '仪表盘',
  document: '文档',
  logout: '退出登录',
  document_project: '项目文档',
  'document_project-link': '项目文档(外链)',
  document_react: 'React文档',
  document_vite: 'Vite文档',
  document_unocss: 'UnoCSS文档',
  document_procomponents: 'ProComponents 文档',
  document_antd: 'Ant Design 文档',
  'user-center': '个人中心',
  about: '关于',
  function: '系统功能',
  function_tab: '标签页',
  'function_multi-tab': '多标签页',
  'function_hide-child': '隐藏子菜单',
  'function_hide-child_one': '隐藏子菜单',
  'function_hide-child_two': '菜单二',
  'function_hide-child_three': '菜单三',
  function_request: '请求',
  'function_toggle-auth': '切换权限',
  'function_super-page': '超级管理员可见',
  manage: '系统管理',
  manage_user: '用户管理',
  'manage_user-detail': '用户详情',
  manage_role: '角色管理',
  manage_menu: '菜单管理',
  'multi-menu': '多级菜单',
  'multi-menu_first': '菜单一',
  'multi-menu_first_child': '菜单一子菜单',
  'multi-menu_second': '菜单二',
  'multi-menu_second_child': '菜单二子菜单',
  'multi-menu_second_child_home': '菜单二子菜单首页',
  exception: '异常页',
  exception_403: '403',
  exception_404: '404',
  exception_500: '500',
  login_register: ' 注册账号',
  'login_code-login': '验证码登录',
  'login_pwd-login': '密码登录',
  'login_reset-pwd': '重置密码',
  ai: 'AI 广告托管',
  ai_listing: 'AI 托管Listing',
  'ai_camtar-listing': 'AI托管Listing详情',
  'ai_target-listing': 'AI托管Target详情',
  ai_dashboard: '广告看板',
  ai_operationrecord: 'AI 操作记录',
  ai_listingall: '所有Listing',

  'ai_children-listingall': '子Listing列表',
  ai_inventory: '库存',
  ai_daily: '数据对比',
  'ai_daily-hosted': '当前托管ASIN数据',
  'ai_daily-nhosted': '取消托管ASIN数据',
  'ai_daily-detail': 'ASIN数据详情',
  management: '管理中心',
  management_auth: '店铺管理',
  management_user: '账户设置',
  'management_buy-auth': '批量激活',
  management_order: '订单管理',
  management_order_rechage: '充值记录',
  management_order_service: '订单记录',
  management_bill: '发票管理',
  management_subaccounts: '子账号管理',
  management_listinghistory: 'AI 托管记录',
  message: '消息中心',
  survey: '问卷调查'
  // "ai_list":"AI 托管Listing",
  // "ai_history":"AI 操作记录"
};

export default route;
