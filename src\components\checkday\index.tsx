import { Card, Select, Typography, DatePicker, Checkbox, Divider } from "antd";
import { Icon } from "@iconify/react";
import { localStg } from "@/utils/storage";
import { ActivedShopInfo } from "@/service/api";
import { getContinentAndCountryName } from "@/utils/useChartData";
import dayjs from "dayjs";

interface CheckDayProps {
  onCountryChange: (country: string[], continentCode: string) => void;
  onDayChange: (value: string[]) => void;
  handleExport: () => void;
  loading?: boolean;
  page?: string;
  multiple?: boolean;
}

const CheckDay = ({
  onCountryChange,
  onDayChange,
  handleExport,
  loading = false,
  page = "",
  multiple = true,
}: CheckDayProps) => {
  const { t } = useTranslation();
  const { Title, Text } = Typography;
  const datePickerRef = useRef<any>(null);
  const selectRef = useRef<any>(null);
  const [defaultValue, setDefaultValue] = useState([]);
  const [selectValue, setSelectValue] = useState([]);
  const [selectedWeekOrMonth, setSelectedWeekOrMonth] = useState("WEEK");
  const [countries, setCountries] = useState([]);
  const dateFormat = "YYYY/MM/DD";

  const [selectAll, setSelectAll] = useState(false);


  const [DatePickerDate, setDatePickerDate] = useState([
    // 获取一个月前
    // dayjs().subtract(1, "month"),
    // 获取七天前
    dayjs().subtract(7, "day"),
    dayjs(),
  ]);

  // 在组件顶部添加缓存有效期常量
  const COUNTRIES_CACHE_EXPIRY = 60 * 60 * 1000; // 1小时


  useEffect(() => {
    if (page.includes("dashboard")) return;
    const cachedCountries = localStg.get('new_countries') as { data: any[]; timestamp: number };
    const now = Date.now();
    if (cachedCountries?.data?.length > 0 && now - cachedCountries.timestamp < COUNTRIES_CACHE_EXPIRY) {
      if (multiple) {
        const localCountryMultiple = localStg.get('localCountryMultiple') as any;
        if (localCountryMultiple?.countryCode) {
          const { countryCode, continentCode } = localCountryMultiple;

          setCountries(cachedCountries.data);
          setDefaultValue(countryCode);
          setSelectValue(countryCode);
          onCountryChange(countryCode, continentCode);
          return;
        }
      } else {
        const localCountry = localStg.get('localCountry') as any;
        if (localCountry?.countryCode) {
          const { countryCode, continentCode } = localCountry;
          // console.log(countryCode, "countryCode====");
          // console.log(continentCode, "continentCode====");
          // console.log(cachedCountries.data, "cachedCountries.data====");
          setCountries(cachedCountries.data);
          setDefaultValue(countryCode);
          setSelectValue(countryCode);
          onCountryChange(countryCode, continentCode);
          return;
        }
      };
    }
    ActivedShopInfo({
      lang: localStg.get("lang") || "zh-CN".split("-")[0],
    }).then((res: any) => {
      if (res && Object.keys(res.data).length > 0) {
        console.log(res.data.AuthCountry, "res.data.AuthCountry====");
        if (Object.keys(res.data.AuthCountry).length > 0) {
          let newCountries: any = []
          Object.values(res.data.AuthCountry).flatMap((item: any) => {
            // console.log(item, "item====")
            if (item.IsAllow) {
              newCountries.push({
                ...item,
                label: getContinentAndCountryName(item.CountryCode).countryName,
                value: item.CountryCode,
                flagUrl: `circle-flags:${item.CountryCode.toLowerCase()}`,
              })
            }
          });
          // 便利 newCountries value 按照 US DE UK IT FR ES JP 排序 这些在前面
          newCountries.sort((a, b) => {
            const order = ['US', 'DE', 'UK', 'IT', 'FR', 'ES', 'JP', 'CN', 'IN']; // Added 'CN' and 'IN' as examples
            const indexA = order.indexOf(a.value);
            const indexB = order.indexOf(b.value);

            // If both countries are in the order list, sort by their index
            if (indexA !== -1 && indexB !== -1) {
              return indexA - indexB;
            }
            // If only one country is in the order list, prioritize it
            if (indexA !== -1) return -1;
            if (indexB !== -1) return 1;
            // If neither country is in the order list, sort alphabetically
            return a.value.localeCompare(b.value);
          });
          // Then sort by activation status
          newCountries.sort((a: any, b: any) => {
            return b.AdServiceActive - a.AdServiceActive; // Assuming IsActivated is a boolean or 0/1
          });
          setCountries(newCountries as any);
          let defaultSelected: any;

          if (multiple) {
            defaultSelected = [newCountries[0].value];
          } else {
            defaultSelected = newCountries[0].value;
          }

          setDefaultValue(defaultSelected);
          setSelectValue(defaultSelected);
          onCountryChange(defaultSelected, getContinentAndCountryName(newCountries[0].CountryCode).continent);

          localStg.set('localCountryMultiple', {
            countryCode: [newCountries[0].CountryCode] || "",
            continentCode: getContinentAndCountryName(newCountries[0].CountryCode).continent || "",
          });
          localStg.set('localCountry', {
            countryCode: newCountries[0].CountryCode || "",
            continentCode: getContinentAndCountryName(newCountries[0].CountryCode).continent || "",
          });
          localStg.set('new_countries', {
            data: newCountries,
            timestamp: now,
          });
        }
      }
    });
  }, []);

  useEffect(() => {
    const dateString = DatePickerDate.map((date: any) =>
      date.format("YYYY-MM-DD"),
    );
    onDayChange(dateString);
  }, [DatePickerDate, selectedWeekOrMonth]);

  const handleCountryChange = (value: string | string[]) => {
    setSelectValue(value as any);
    setSelectAll(value.length === countries.length);

    let country: any = null;
    if (multiple) {
      country = (value as any).map((item: any) =>
        countries.find((country: any) => country.value === item),
      );
      // console.log(country, "多选country====");
    } else {
      country = countries.find((country: any) => country.value === value);
      // console.log(country, "单选country====");
    }
    localStg.set(multiple ? 'localCountryMultiple' : 'localCountry', {
      countryCode: value,
      continentCode: country.ContinentCode || "",
    });
    onCountryChange(value as any, country.ContinentCode);
  };

  const onChange = (value: any, dateString: any) => {
    setDatePickerDate(value);
  };


  const handleCheckboxChange = (e) => {
    if (selectValue.length === countries.length) {
      // Deselect all
      setSelectValue([]);
      onCountryChange([], "");
    } else {
      // Select all
      const allCountryValues = countries.map((country) => country.value);
      setSelectValue(allCountryValues);
      onCountryChange(allCountryValues, ""); // Assuming continentCode is not needed for all
    }
  };

  return (
    <>
      <Card >
        <div className="flex items-center justify-between">
          <Select
            ref={selectRef}
            style={{
              maxWidth: "450px",
              minWidth: "220px",
              borderRadius: "5px",
            }}
            onChange={handleCountryChange}
            disabled={loading}
            defaultValue={defaultValue}
            key={defaultValue}
            maxTagCount={4}
            optionLabelProp="title"
            mode={multiple ? "multiple" : "default"}
            value={selectValue}
            // 添加自定义选择框内容渲染
            suffixIcon={!multiple && selectValue ? (
              <Icon
                icon={countries.find((c: any) => c.value === selectValue)?.flagUrl}
                width={22}
                height={22}
              />
            ) : undefined}
            tagRender={(props) => {
              const { label, value, closable, onClose } = props;
              const country = countries.find((c: any) => c.value === value);
              // console.log(country, "country====");
              return (
                <div className="ant-select-selection-item">
                  <Icon
                    className="mr-1"
                    icon={country?.flagUrl}
                    width={22}
                    height={22}
                  />
                  <span>{t(`page.setting.country.${value}`)}</span>
                  {closable && (
                    <span
                      className="ant-select-selection-item-remove text-lg"
                      onClick={onClose}
                    >
                      ×
                    </span>
                  )}
                </div>
              );
            }}
            dropdownRender={(menu) => (
              <>
                {multiple && (
                  <div style={{ padding: '4px 8px 8px 12px', cursor: 'pointer' }}>
                    <Checkbox
                      checked={selectAll}
                      onChange={(e) => {
                        if (e.target.checked) {
                          const allValues = countries.map((country: any) => country.value);
                          setSelectValue(allValues);
                          setDefaultValue(allValues);
                          setSelectAll(true);
                          onCountryChange(allValues, countries[0]?.ContinentCode || "");
                        } else {
                          setSelectValue([]);
                          setDefaultValue([]);
                          setSelectAll(false);
                          onCountryChange([], "");
                        }
                      }}
                    >
                      全选
                    </Checkbox>
                  </div>
                )}
                <Divider style={{ margin: '0' }} />
                {menu}
              </>
            )}
          >
            {countries.map((country: any) => (
              <Select.Option key={country.value} value={country.value} title={t(`page.setting.country.${country.value}`)}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Icon
                      className="mr-2"
                      icon={country.flagUrl}
                      width={22}
                      height={22}
                    />
                    <span>{t(`page.setting.country.${country.value}`)}</span>
                  </div>
                  {!!country.AdServiceActive && <ATag bordered={false} color="processing">{t('page.listingall.search.activated')}</ATag>}
                </div>
              </Select.Option>
            ))}
          </Select>
          {/* 受Amazon官方数据影响，近三天数据可能存在偏差 */}
          {page == "weekly" && (
            <Title level={5} style={{ margin: 0 }}>受Amazon官方数据影响，近三天数据可能存在偏差</Title>
          )}
          {
            // page为weekly时，不显示时间选择器
            page !== "weekly" && (
              <DatePicker.RangePicker
                disabled={loading}
                allowClear={false}
                onChange={onChange}
                defaultValue={DatePickerDate}
                format={dateFormat}
              />
            )}
        </div>
      </Card>
    </>
  );
};

export default CheckDay;
