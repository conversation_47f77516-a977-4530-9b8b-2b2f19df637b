/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router


import type { LazyRouteFunction, RouteObject } from "react-router-dom";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";
type CustomRouteObject = Omit<RouteObject, 'Component'|'index'> & {
  Component?: React.ComponentType<any>|null;
};

export const layouts: Record<RouteLayout, LazyRouteFunction<CustomRouteObject>> = {
  base: () => import("@/layouts/base-layout/index.tsx"),
  blank: () => import("@/layouts/blank-layout/index.tsx"),
};

export const pages: Record<LastLevelRouteKey, LazyRouteFunction<CustomRouteObject>> = {
  403: () => import("@/pages/_builtin/403/index.tsx"),
  404: () => import("@/pages/_builtin/404/index.tsx"),
  500: () => import("@/pages/_builtin/500/index.tsx"),
  "iframe-page": () => import("@/pages/_builtin/iframe-page/[url].tsx"),
  admin: () => import("@/pages/admin/index.tsx"),
  "ai_camtar-listing": () => import("@/pages/ai/camtar-listing/index.tsx"),
  "ai_children-listingall": () => import("@/pages/ai/children-listingall/index.tsx"),
  "ai_daily-detail": () => import("@/pages/ai/daily-detail/index.tsx"),
  "ai_daily-hosted": () => import("@/pages/ai/daily-hosted/index.tsx"),
  "ai_daily-nhosted": () => import("@/pages/ai/daily-nhosted/index.tsx"),
  ai_daily: () => import("@/pages/ai/daily/index.tsx"),
  ai_inventory: () => import("@/pages/ai/inventory/index.tsx"),
  ai_listing: () => import("@/pages/ai/listing/index.tsx"),
  ai_listingall: () => import("@/pages/ai/listingall/index.tsx"),
  ai_operationrecord: () => import("@/pages/ai/operationrecord/index.tsx"),
  "ai_target-listing": () => import("@/pages/ai/target-listing/index.tsx"),
  home: () => import("@/pages/home/<USER>"),
  "login_captch-qr-code": () => import("@/pages/login/captch-qr-code/index.tsx"),
  "login_code-login": () => import("@/pages/login/code-login/index.tsx"),
  login: () => import("@/pages/login/index.tsx"),
  login_logo: () => import("@/pages/login/logo/index.tsx"),
  "login_power-by": () => import("@/pages/login/power-by/index.tsx"),
  "login_pwd-login": () => import("@/pages/login/pwd-login/index.tsx"),
  login_register: () => import("@/pages/login/register/index.tsx"),
  "login_reset-pwd": () => import("@/pages/login/reset-pwd/index.tsx"),
  manage_menu: () => import("@/pages/manage/menu/index.tsx"),
  manage_role: () => import("@/pages/manage/role/index.tsx"),
  management_auth: () => import("@/pages/management/auth/index.tsx"),
  management_bill: () => import("@/pages/management/bill/index.tsx"),
  "management_buy-auth": () => import("@/pages/management/buy-auth/index.tsx"),
  management_listinghistory: () => import("@/pages/management/listinghistory/index.tsx"),
  management_order_rechage: () => import("@/pages/management/order/rechage/index.tsx"),
  management_order_service: () => import("@/pages/management/order/service/index.tsx"),
  management_subaccounts: () => import("@/pages/management/subaccounts/index.tsx"),
  management_user: () => import("@/pages/management/user/index.tsx"),
  message: () => import("@/pages/message/index.tsx"),
  survey: () => import("@/pages/survey/index.tsx"),
};
