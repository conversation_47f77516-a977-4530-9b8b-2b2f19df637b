import CheckInventory from '@/components/check-Inventory';

import { useChartData } from '@/utils/useChartData';
import { selectUserInfo } from '@/store/slice/auth';
import {
  createFBAInventory,
  createInventoryAge,
} from "@/components/weekly-vchart/chart"
import { setChartCache, selectChartCache } from '@/store/slice/chart';
import VchartMain from '@/components/vchart-main';
import { fetchChartData } from '@/utils/fetchChartData';
import dayjs from "dayjs";
export function Component() {
  const { Sider, Header, Content } = ALayout;
  const { t } = useTranslation();
  const [headerLoading, setHeaderLoading] = useState(true);
  const chartCache = useAppSelector(selectChartCache);
  const dispatch = useAppDispatch();
  const [selectedCountry, setSelectedCountry] = useState([]);
  const [charts, setCharts] = useState<any[]>([]);
  const userInfo = useAppSelector(selectUserInfo);

  const handleChartData = useChartData(selectedCountry);
  const handleCountryChange = (key: any) => {
    console.log(key, "key");
    setSelectedCountry(key);
  };


  const fetchData = async () => {
    let DataTypeList = [
      // 订单数据
      // fba库存数据 
      { name: "W-FBA", loading: true, StartDate: `>${dayjs().subtract(36, 'day').format('YYYY-MM-DD')}`, chart: createFBAInventory(userInfo?.IsDistributor) },
      // // 库龄数据
      { name: "W-KL", loading: true, StartDate: `>${dayjs().subtract(40, 'day').format('YYYY-MM-DD')}`, chart: createInventoryAge(), merge: true },
    ] as any[];
    console.log(DataTypeList, "DataTypeList");
    if (userInfo?.IsDistributor) {
      DataTypeList = DataTypeList.filter((item) => item.name !== "W-KL");
      DataTypeList[0].height = window.innerHeight - 180;
    }

    try {
      const mergedResults = await fetchChartData(selectedCountry, "", DataTypeList, chartCache, handleChartData, dispatch, setChartCache, "inventory");
      // console.log(mergedResults, "mergedResults.length");
      setTimeout(() => {
        setCharts(mergedResults);
        setHeaderLoading(false);
      }, 1000);
    } catch (error) {
      setHeaderLoading(false);
    }
  };

  const initData = () => {
    let defaultCharts = [
      { name: "W-FBA", loading: true, chart: createFBAInventory(userInfo?.IsDistributor) },
      // 如果userInfo?.IsDistributor为true，则不显示W-KL，否则显示
      { name: "W-KL", loading: true, chart: createInventoryAge(selectedCountry, selectedCountry) }
    ]
    if (userInfo?.IsDistributor) {
      defaultCharts = defaultCharts.filter((item) => item.name !== "W-KL");
    }
    setHeaderLoading(true);
    setCharts(defaultCharts);
  }
  useEffect(() => {
    console.log(selectedCountry, "selectedCountry");
    if (selectedCountry.length === 0) {
      return;
    }
    initData();
    fetchData();
  }, [selectedCountry]);
  return (
    <div className="">

      <ALayout className="h-full">
        <div className="sticky top-0 z-10 w-full">
          <CheckInventory
            onCountryChange={handleCountryChange}
            loading={headerLoading}
            page=""
          />
        </div>

        <ACard className="h-full mt-2" shadows={"always"}>
          {/* <Sider
          style={{ paddingRight: "10px" }}
          >
            <TreeList page="inventory" onTreeItemChange={handleTreeItemChange} />
          </Sider> */}
          <Content>
            {/* 可售天数颜色说明 */}
  <div className="bg mb-2">
    <h3 className="text-center font-bold mb-2">{t('page.inventory.sellableDays.title')}</h3>
    <div className="flex items-center justify-center gap-4">
      <div className="flex items-center gap-2">
        <span className="bg-#F44336 w-5 h-5 rounded-full inline-block"></span>
        <span>{t('page.inventory.sellableDays.days360')}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="bg-#FF7043 w-5 h-5 rounded-full inline-block"></span>
        <span>{t('page.inventory.sellableDays.days270')}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="bg-#FFA726 w-5 h-5 rounded-full inline-block"></span>
        <span>{t('page.inventory.sellableDays.days180')}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="bg-#1664ff w-5 h-5 rounded-full inline-block"></span>
        <span>{t('page.inventory.sellableDays.days90')}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="bg-#66BB6A w-5 h-5 rounded-full inline-block"></span>
        <span>{t('page.inventory.sellableDays.days1')}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="bg-#333 w-5 h-5 rounded-full inline-block"></span>
        <span>{t('page.inventory.sellableDays.noData')}</span>
      </div>
    </div>
  </div>




            <div>
              {charts.map((item: any) => (
                <VchartMain item={item} key={item.name} />
              ))}
            </div>
          </Content>
        </ACard>
      </ALayout>

    </div>
  );
}
