import { Popover } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

const ColorExplanation = () => {
  const { t } = useTranslation();

  const explanations = [
    {
      color: '#66BB6A',
      acos: t('page.ailisting.colorExplanation.items.lowAcosHighTraffic.acos'),
      condition: t('page.ailisting.colorExplanation.items.lowAcosHighTraffic.condition'),
      strategy: t('page.ailisting.colorExplanation.items.lowAcosHighTraffic.strategy')
    },
    {
      color: '#1664ff',
      acos: t('page.ailisting.colorExplanation.items.lowAcosLowTraffic.acos'),
      condition: t('page.ailisting.colorExplanation.items.lowAcosLowTraffic.condition'),
      strategy: t('page.ailisting.colorExplanation.items.lowAcosLowTraffic.strategy')
    },
    {
      color: '#FFA726',
      acos: t('page.ailisting.colorExplanation.items.highAcosHighTraffic.acos'),
      condition: t('page.ailisting.colorExplanation.items.highAcosHighTraffic.condition'),
      strategy: t('page.ailisting.colorExplanation.items.highAcosHighTraffic.strategy')
    },
    {
      color: '#F44336',
      acos: t('page.ailisting.colorExplanation.items.highAcosLowTraffic.acos'),
      condition: t('page.ailisting.colorExplanation.items.highAcosLowTraffic.condition'),
      strategy: t('page.ailisting.colorExplanation.items.highAcosLowTraffic.strategy')
    },
    {
      color: '#ccc',
      acos: t('page.ailisting.colorExplanation.items.lowInventory.acos'),
      condition: t('page.ailisting.colorExplanation.items.lowInventory.condition'),
      strategy: t('page.ailisting.colorExplanation.items.lowInventory.strategy')
    },
    {
      color: '#f8f8f8',
      acos: t('page.ailisting.colorExplanation.items.dataCollecting.acos'),
      condition: t('page.ailisting.colorExplanation.items.dataCollecting.condition'),
      strategy: t('page.ailisting.colorExplanation.items.dataCollecting.strategy')
    }
  ];

  const content = (
    <div style={{ padding: '16px', borderRadius: '8px' }}>
      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
        <thead>
          <tr>
            <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>
              {t('page.ailisting.colorExplanation.tableColumns.colorCategory')}
            </th>
            <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>
              {t('page.ailisting.colorExplanation.tableColumns.acosCondition')}
            </th>
            <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>
              {t('page.ailisting.colorExplanation.tableColumns.trafficUnitCondition')}
            </th>
            <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>
              {t('page.ailisting.colorExplanation.tableColumns.strategy')}
            </th>
          </tr>
        </thead>
        <tbody>
          {explanations.map((item, index) => (
            <tr key={index}>
              <td style={{ padding: '8px', textAlign: 'left' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ width: '20px', height: '20px', backgroundColor: item.color, marginRight: '8px' }}></div>
                </div>
              </td>
              <td style={{ padding: '8px', textAlign: 'left' }}>{item.acos}</td>
              <td style={{ padding: '8px', textAlign: 'left' }}>{item.condition}</td>
              <td style={{ padding: '8px', textAlign: 'left' }}>{item.strategy}</td>
            </tr>
          ))}
        </tbody>
      </table>
      <p style={{ marginTop: '16px', fontSize: '14px', color: '#666', textAlign: 'left' }}>
        {t('page.ailisting.colorExplanation.trafficUnitDefinition')}
      </p>
      <p style={{ fontSize: '14px', color: '#666', textAlign: 'left' }}>
        {t('page.ailisting.colorExplanation.sufficientTrafficDefinition')}
      </p>
      <p style={{ fontSize: '14px', color: '#333', fontWeight: '500', textAlign: 'left' }}>
        {t('page.ailisting.colorExplanation.colorClassificationBasis')}
      </p>
    </div>
  );

  return (
    <Popover
      content={content}
      title=""
      trigger="hover"
      placement="bottomLeft"
    >
      <p className="mt-1 flex cursor-pointer items-center gap-3px text-12px text-gray-400">
        <span>{t('page.ailisting.colorExplanation.defaultSorting')}</span>
        <InfoCircleOutlined />
      </p>
    </Popover>
  );
};

export default ColorExplanation;
