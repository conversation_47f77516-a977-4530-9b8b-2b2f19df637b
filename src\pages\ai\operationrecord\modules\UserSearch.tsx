import { Button, Col, Flex, Form, Row, Select, DatePicker } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import { ActivedShopInfo } from '@/service/api';
import dayjs from 'dayjs';
import { selectUserInfo } from '@/store/slice/auth';
import { localStg } from "@/utils/storage";
interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
  suffix?: React.ReactNode;
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading = true, suffix }) => {
  const [countries, setCountries] = useState([]);
  const [defaultCountry, setDefaultCountry] = useState<string | undefined>(undefined);
  const userInfo = useAppSelector(selectUserInfo);
  // 在组件顶部添加缓存有效期常量
  const COUNTRIES_CACHE_EXPIRY = 60 * 60 * 1000; // 1小时
  const { t } = useTranslation();


  useEffect(() => {
    form.setFieldsValue({ date: dayjs().subtract(1, 'day') }); // 设置默认日期为今天
    const cachedCountries = localStg.get('new_countries') as { data: any[]; timestamp: number };
    const now = Date.now();
    if (cachedCountries?.data?.length > 0 && now - cachedCountries.timestamp < COUNTRIES_CACHE_EXPIRY) {
      
      const localCountry = localStg.get('localCountry') as any;
      if (localCountry?.countryCode) {
        let { countryCode, continentCode } = localCountry;
        setCountries(cachedCountries.data);
        setDefaultCountry(countryCode);
        form.setFieldsValue({ country: countryCode });
        handleSearch(cachedCountries.data, countryCode);
        return;
      }
    }
    ActivedShopInfo({
      lang: 'zh-CN'.split('-')[0],
    }).then((res: any) => {
      if (res && Object.keys(res.data).length > 0) {
        if (Object.keys(res.data.AuthCountry).length > 0) {
          let newCountries: any = [];
          Object.values(res.data.AuthCountry).flatMap((item: any) => {
            if (item.IsAllow) {
              newCountries.push({
                ...item,
                label: t('page.setting.country.'+item.CountryCode),
                value: item.CountryCode,
                flagUrl: `circle-flags:${item.CountryCode.toLowerCase()}`,
              });
            }
          });
          newCountries.sort((a: any, b: any) => {
            const order = ['US', 'DE', 'UK', 'IT', 'FR', 'ES', 'JP', 'CN', 'IN'];
            const indexA = order.indexOf(a.value);
            const indexB = order.indexOf(b.value);
            if (indexA !== -1 && indexB !== -1) {
              return indexA - indexB;
            }
            if (indexA !== -1) return -1;
            if (indexB !== -1) return 1;
            return a.value.localeCompare(b.value);
          });
          newCountries.sort((a: any, b: any) => {
            return b.AdServiceActive - a.AdServiceActive;
          });
          setCountries(newCountries);
          if (newCountries.length > 0) {
            localStg.set('new_countries', {
              data: newCountries,
              timestamp: now,
            });
            setDefaultCountry(newCountries[0].value);
            form.setFieldsValue({ country: newCountries[0].value });
            handleSearch(newCountries);
          }
        }
      }
    });
  }, [form]);

  const handleSearch = (localCountries: any = [], countryCode: string = '') => {
    const values = form.getFieldsValue();
    let newCountries = countries;
    if (countryCode) {
      values.country = countryCode;
    }
    if (localCountries.length > 0) {
      newCountries = localCountries;
    }
    form.setFieldsValue({ ContinentCode:  t('page.setting.country.'+values.country) });
    const params = {
      UID: userInfo.active_shop_id,
      CountryCode: values.country,
      Date: values.date ? values.date.format('YYYY-MM-DD') : undefined,
    };
    localStg.set('localCountry', {
      countryCode: values.country,
      continentCode: t('page.setting.country.'+values.country),
    });
    search(params);
  };

  const disabledDate = (current: any) => {
    return current && current > dayjs().endOf('day');
  };

  return (
    <Form form={form} disabled={loading}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={6} lg={5}>
          <Form.Item className="m-0 text-start" name="country" label={t('page.operationRecord.search.country')}>
            <Select
              placeholder={t('page.listingall.search.selectCountry')}
              allowClear={false}
              onChange={(value) => handleSearch(countries)}
            >
              {countries.map((country: any) => (
                <Select.Option key={country.value} value={country.value} title={country.label}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Icon
                        className="mr-2"
                        icon={country.flagUrl}
                        width={22}
                        height={22}
                      />
                      {country.label}
                    </div>
                    {!!country.AdServiceActive && <ATag bordered={false} color="processing">{t('page.listingall.search.activated')}</ATag>}
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={8}>
          <Form.Item className="m-0" name="date" label={t('page.operationRecord.search.date')}>
            <DatePicker
              allowClear={false}
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              onChange={() => handleSearch(countries)}
            />
          </Form.Item>
        </Col>

        <Col span={24} lg={suffix ? 12 : 4}>
          <Flex justify="end">
            {suffix}
          </Flex>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;