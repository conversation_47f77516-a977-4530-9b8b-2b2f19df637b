import { Suspense, lazy } from 'react';
import { Popover } from 'antd';
import type { DropdownProps } from 'antd';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  QuestionCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import BigNumber from 'bignumber.js';
import { Icon } from '@iconify/react';
import moment from 'moment';

import { localStg } from '@/utils/storage';
import {
  AuthAsinCancel,
  AuthAsinGetUpdate,
  AuthAsinOperate,
  AuthAsinSetShopPreAcos,
  getAuthedListingsSum
} from '@/service/api';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { useAsinOptions, useGetStrategyLabel } from './modules/until';
const NewAntdModalComponent = lazy(() => import('./modules/NewModalComponent'));
const ColorExplanation = lazy(() => import('./modules/ColorExplanation'));
const SelectAcos = lazy(() => import('./modules/SelectAcos'));
const AcosSettingModal = lazy(() => import('./modules/AcosSettingModal'));
const FunnelChartIcon = lazy(() => import('./modules/FunnelChartIcon'));
const CompetitorStrategyConfig = lazy(() => import('./modules/CompetitorStrategyConfig'));
const AsinStatusSetter = lazy(() => import('./modules/AsinStatusSetter'));
const AiDiagnosisModal = lazy(() => import('./modules/AiDiagnosisModal'));
const TimeBudgetModal = lazy(() => import('./modules/TimeBudgetModal'));

export function Component() {
  const userInfo = localStg.get('userInfo') || {};
  const storageKey = `table_columns_${Number(userInfo?.active_shop_id)}_ai_listing`;
  const { t } = useTranslation();
  const asinOptions = useAsinOptions();
  const getStrategyLabel = useGetStrategyLabel();

  const [isShowContrast, setisShowContrast] = useState(true);

  const { tableWrapperRef, scrollConfig } = useTableScroll();

  const navigate = useNavigate();
  // 批量操作弹窗
  const [open, setOpen] = useState(false);
  // 设置整体ACOS
  const [shopPreAcos, setShopPreAcos] = useState(24);
  // 库存最小限制
  const [asinLimitStore, setAsinLimitStore] = useState(0);
  // listing ACOS
  // const [listingPreAcos, setListingPreAcos] = useState(0);
  // 自定义ACOS
  // const [customAcos, setCustomAcos] = useState(0);
  //
  const acosModalRef = useRef(null);

  // 存储当前操作的keys，用于ACOS设置
  const operationKeysRef = useRef([]);

  //
  const [selectedAcos, setSelectedAcos] = useState(shopPreAcos); // 新增状态
  const selectedAcosRef = useRef(selectedAcos);
  useEffect(() => {
    selectedAcosRef.current = selectedAcos;
  }, [selectedAcos]);

  // Add ref for AI diagnosis modal
  const aiDiagnosisModalRef = useRef<{ open: () => void; close: () => void }>(null);

  // 获取存储的列设置
  const getStoredColumnKeys = () => {
    try {
      const stored = localStorage.getItem(storageKey);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Error parsing stored column keys:', error);
      return null;
    }
  };

  // 保存列设置到本地存储
  const saveColumnKeys = (checks: AntDesign.TableColumnCheck[]) => {
    try {
      // 只保存选中的列的key
      const selectedKeys = checks.filter(check => check.checked).map(check => check.key);
      console.log(selectedKeys, 'selectedKeys====');
      console.log(storageKey, 'storageKey=======');
      localStorage.setItem(storageKey, JSON.stringify(selectedKeys));
    } catch (error) {
      console.error('Error saving column keys:', error);
    }
  };

  // 包装 setColumnChecks
  const handleSetColumnChecks = (checks: AntDesign.TableColumnCheck[]) => {
    console.log(checks, 'checks====');
    saveColumnKeys(checks);
    setColumnChecks(checks);
  };

  const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
    {
      apiFn: getAuthedListingsSum,
      apiParams: {},
      immediate: false,
      columns: () => {
        const defaultColumns = [
          // {
          //   key: 'ID',
          //   title:"ID",
          //   dataIndex: 'ID',
          //   align: 'center',
          //   hidden: true
          // },
          {
            key: 'asin_state',
            dataIndex: 'asin_state',
            title: t('page.ailisting.columns.parentAsin'),
            align: 'center',
            width: 400,
            fixed: 'left',
            checked: true,
            showSorterTooltip: false,
            sortDirections: ['descend'],
            sorter: (a: any, b: any) => {
              return b.asin_state - a.asin_state;
            },
            render: (_, record) => {
              // 定义asin 颜色状态
              return (
                <div className="h-full flex cursor-pointer items-stretch gap-2">
                  {
                    <div
                      style={{
                        width: '6px',
                        // height: '15px',
                        backgroundColor: getColor(record.asin_state),
                        borderRadius: '10px',
                        border: '1px solid #f1f1f1',
                        // 加上阴影
                        boxShadow: '0 0 10px 0 rgba(0, 0, 0, 0.1)'
                      }}
                    ></div>
                  }
                  <LazyImage
                    src={record.first_image}
                    alt={record.item_name || '--'}
                    className="max-h-[100px] min-w-[80px] object-contain"
                    width={100}
                  />
                  <div
                    onClick={event => {
                      // 检查是否有文本被选中
                      const selection = window.getSelection();
                      if (selection && selection.toString().length > 0) {
                        return; // 如果有文本被选中，则不执行跳转
                      }

                      // 在导航前关闭所有 ATooltip
                      document.querySelectorAll('.ant-tooltip').forEach(element => {
                        element.style.display = 'none';
                      });

                      // 延迟一点导航，确保 ATooltip 有时间关闭
                      setTimeout(() => {
                        navigate(
                          `/ai/camtar-listing?market=${form.getFieldValue('country')}&asin=${record.parent_asin}&isAuth=${record.IsAuth}`,
                          '_blank'
                        );
                      }, 0);

                      // router.push({name:"ai_camtar-listing",query:{market:form.getFieldValue('country'),asin:record.parent_asin}})
                    }}
                    className="w-full flex flex-col justify-between"
                  >
                    <ATooltip
                      placement="topLeft"
                      title={record.item_name}
                    >
                      <div className="line-clamp-2 text-left">{record.item_name || '--'}</div>
                    </ATooltip>
                    <div className="flex flex-nowrap text-start">
                      <span className="mr-1 whitespace-nowrap text-gray-400">{t('page.listingall.column.price')}:</span>
                      <div className="flex flex-nowrap">
                        {record.min_price === record.max_price ? (
                          <CurrencySymbol
                            countryCode={form.getFieldValue('country')}
                            value={record.min_price}
                          />
                        ) : (
                          <>
                            <CurrencySymbol
                              countryCode={form.getFieldValue('country')}
                              value={record.min_price}
                            />
                            ~
                            <CurrencySymbol
                              countryCode={form.getFieldValue('country')}
                              value={record.max_price}
                            />
                          </>
                        )}
                      </div>
                      <span className="mx-1"> | </span>
                      <span className="whitespace-nowrap">
                        <span className="mr-1 text-gray-400">{t('page.listingall.column.inventory')}:</span>
                        {record.HaveNum || '0'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-start">
                      <p className="flex items-center">
                        {t('page.ailisting.columns.parentAsin')}:
                        <a
                          className="text-primary"
                          target="_blank"
                          rel="noopener noreferrer"
                          onClick={event => {
                            event.stopPropagation();
                            if (record.parent_asin && record.parent_asin !== '-') {
                              window.open(
                                `https://www.${getSalesChannel(form.getFieldValue('country'))}/dp/${record.parent_asin}`,
                                '_blank'
                              );
                            }
                          }}
                        >
                          {record.parent_asin || '-'}
                        </a>
                        {record?.ErrorInfo && (
                          <Popover
                            placement="right"
                            title={t('page.ailisting.tooltips.errorPrompt')}
                            content={record.ErrorInfo}
                          >
                            <ExclamationCircleOutlined
                              className="ml-1 text-[#ff4d4f]"
                              style={{ fontSize: '14px' }}
                            />
                          </Popover>
                        )}
                      </p>
                      <span className="ml-2 text-[12px] text-gray-400">
                        {record.LastUpdateDatetime
                          ? moment(record.LastUpdateDatetime, 'YYYY-MM-DD HH:mm:ss').format('YYYY.MM.DD').substring(2)
                          : ''}
                        {['6', '7', '8'].includes(String(record.ManageState))
                          ? t('page.ailisting.status.paused')
                          : t('page.ailisting.status.hosted')}
                      </span>
                    </div>
                  </div>
                </div>
              );
            }
          },
          {
            key: '总销售额',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.totalSales')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.totalSales')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.totalSales')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            dataIndex: '总销售额',
            showSorterTooltip: false,
            width: 120,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.总销售额 || 0) - Number.parseFloat(b.总销售额 || 0);
            },
            checked: true,
            render: (text: string, record: any) => {
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={text}
                  oldValue={record.old_总销售额}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: '广告总销售额',
            dataIndex: '广告总销售额',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.adSales')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.adSales')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.adSales')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 110,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.广告总销售额 || 0) - Number.parseFloat(b.广告总销售额 || 0);
            },
            checked: true,
            render: (text: string, record: any) => {
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={text}
                  oldValue={record.old_广告总销售额}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: '广告总花费',
            dataIndex: '广告总花费',
            align: 'center',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.adSpend')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.adSpend')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.adSpend')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            width: 110,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.广告总花费 || 0) - Number.parseFloat(b.广告总花费 || 0);
            },
            checked: false,
            render: (_, record) => {
              // 保留整数
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={Number.parseInt(record.广告总花费 || 0).toFixed(2)}
                  oldValue={Number.parseInt(record.old_广告总花费 || 0).toFixed(2)}
                  isShowContrast={isShowContrast}
                  reverseColors={true}
                />
              );
            }
          },
          {
            key: '自然销售额',
            dataIndex: '自然销售额',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.organicSales')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.organicSales')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.organicSales')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            showSorterTooltip: false,
            align: 'center',
            width: 130,
            checked: true,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.自然销售额 || 0) - Number.parseFloat(b.自然销售额 || 0);
            },
            render: (text: string, record: any) => {
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={text}
                  oldValue={record.old_自然销售额}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: '自然销售比例',
            dataIndex: '自然销售比例',
            showSorterTooltip: false,
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.organicSalesRatio')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.organicSalesRatio')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.organicSalesRatio')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 170,
            sorter: (a: any, b: any) => {
              const valueA = typeof a.自然销售比例 === 'string' ? a.自然销售比例.replace('%', '') : 0;
              const valueB = typeof b.自然销售比例 === 'string' ? b.自然销售比例.replace('%', '') : 0;
              return Number(valueA) - Number(valueB);
            },
            checked: false,
            render: (text: string, record: any) => {
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={text || '0%'}
                  oldValue={record.old_自然销售比例 || '0%'}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },

          {
            key: 'TACOS',
            dataIndex: 'TACOS',
            showSorterTooltip: false,
            title: () => (
              <span>
                <Popover
                  title="TACOS"
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.tacos')}</li>
                    </ul>
                  }
                >
                  {'TACOS'}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 90,
            sorter: (a: any, b: any) => {
              const valueA = typeof a.TACOS === 'string' ? a.TACOS.replace('%', '') : 0;
              const valueB = typeof b.TACOS === 'string' ? b.TACOS.replace('%', '') : 0;
              return Number(valueA) - Number(valueB);
            },
            checked: true,
            render: (text: string, record: any) => {
              const spend = new BigNumber(record.广告总花费 || 0);
              const sales = new BigNumber(record.总销售额 || 0);
              const tacos = `${spend.dividedBy(sales).multipliedBy(100).toFixed(2)}%`;
              // 计算old
              const oldSpend = new BigNumber(record.old_广告总花费 || 0);
              const oldSales = new BigNumber(record.old_总销售额 || 0);
              const oldTacos = `${oldSpend.dividedBy(oldSales).multipliedBy(100).toFixed(2)}%`;
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={tacos}
                  oldValue={oldTacos}
                  isShowContrast={isShowContrast}
                  reverseColors={true}
                />
              );
            }
          },
          {
            key: 'Deep_SP_ACOS',
            dataIndex: 'Deep_SP_ACOS',
            showSorterTooltip: false,
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.aiAcosSp')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.aiAcosSp')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.aiAcosSp')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 130,
            checked: true,

            sorter: (a: any, b: any) => {
              const valueA = typeof a.Deep_SP_ACOS === 'string' ? a.Deep_SP_ACOS.replace('%', '') : 0;
              const valueB = typeof b.Deep_SP_ACOS === 'string' ? b.Deep_SP_ACOS.replace('%', '') : 0;
              return Number(valueA) - Number(valueB);
            },
            render: (text: string, record: any) => {
              const spend = new BigNumber(record.Deep_SP广告花费 || 0);
              const sales = new BigNumber(record.Deep_SP广告销售额 || 0);
              const acos = `${spend.dividedBy(sales).multipliedBy(100).toFixed(2)}%`;
              // 计算old
              const oldSpend = new BigNumber(record.old_Deep_SP广告花费 || 0);
              const oldSales = new BigNumber(record.old_Deep_SP广告销售额 || 0);
              const oldAcos = `${oldSpend.dividedBy(oldSales).multipliedBy(100).toFixed(2)}%`;
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={acos}
                  oldValue={oldAcos}
                  isShowContrast={isShowContrast}
                  reverseColors={true}
                />
              );
            }
          },

          {
            key: 'Deep_SP广告花费',
            dataIndex: 'Deep_SP广告花费',
            title: t('page.ailisting.columns.aiAdSpendSp'),
            align: 'center',
            width: 140,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.Deep_SP广告花费 || 0) - Number.parseFloat(b.Deep_SP广告花费 || 0);
            },
            checked: true,
            render: (_, record) => {
              // 保留整数
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={record.Deep_SP广告花费}
                  oldValue={record.old_Deep_SP广告花费}
                  isShowContrast={isShowContrast}
                  reverseColors={true}
                />
              );
            }
          },

          {
            key: '非Deep_SP_ACOS',
            showSorterTooltip: false,
            dataIndex: '非Deep_SP_ACOS',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.nonAiAcosSp')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.nonAiAcosSp')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.nonAiAcosSp')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 160,
            checked: true,
            sorter: (a: any, b: any) => {
              const valueA = typeof a.非Deep_SP_ACOS === 'string' ? a.非Deep_SP_ACOS.replace('%', '') : 0;
              const valueB = typeof b.非Deep_SP_ACOS === 'string' ? b.非Deep_SP_ACOS.replace('%', '') : 0;
              return Number(valueA) - Number(valueB);
            },
            render: (text: string, record: any) => {
              const spend = new BigNumber(record.非Deep_SP_广告花费 || 0);
              const sales = new BigNumber(record.非Deep_SP_广告销售额 || 0);
              const acos = `${spend.dividedBy(sales).multipliedBy(100).toFixed(2)}%`;
              // console.log(record.非Deep_SP_广告花费,record.非Deep_SP_广告销售额,acos,"acos=====");
              // 计算old
              const oldSpend = new BigNumber(record.old_非Deep_SP_广告花费 || 0);
              const oldSales = new BigNumber(record.old_非Deep_SP_广告销售额 || 0);
              const oldAcos = `${oldSpend.dividedBy(oldSales).multipliedBy(100).toFixed(2)}%`;
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={acos}
                  oldValue={oldAcos}
                  isShowContrast={isShowContrast}
                  reverseColors={true}
                />
              );
            }
          },
          {
            key: 'SP广告销售额占比',
            dataIndex: 'SP广告销售额占比',
            showSorterTooltip: false,
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.spAdSalesRatio')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.spAdSalesRatio')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.spAdSalesRatio')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 150,
            checked: false,
            sorter: (a: any, b: any) => {
              const valueA = typeof a['SP广告销售额占比'] === 'string' ? a['SP广告销售额占比'].replace('%', '') : 0;
              const valueB = typeof b['SP广告销售额占比'] === 'string' ? b['SP广告销售额占比'].replace('%', '') : 0;
              return Number(valueA) - Number(valueB);
            },
            render: (text: string, record: any) => {
              return (
                <CurrencySymbol
                  value={text || '0%'}
                  oldValue={record.old_SP广告销售额占比 || '0%'}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: 'Deep_SP广告销售额',
            dataIndex: 'Deep_SP广告销售额',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.aiAdSales')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.aiAdSales')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.aiAdSales')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 120,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.Deep_SP广告销售额 || 0) - Number.parseFloat(b.Deep_SP广告销售额 || 0);
            },
            checked: true,
            render: (_, record) => {
              // 保留整数
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={record.Deep_SP广告销售额}
                  oldValue={record.old_Deep_SP广告销售额}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: 'DeepSP广告销售额在SP中占比',
            showSorterTooltip: false,
            dataIndex: 'DeepSP广告销售额在SP中占比',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.aiSalesRatio')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.aiSalesRatio')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.aiSalesRatio')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 130,
            checked: true,
            sorter: (a: any, b: any) => {
              const valueA =
                typeof a['DeepSP广告销售额在SP中占比'] === 'string'
                  ? a['DeepSP广告销售额在SP中占比'].replace('%', '')
                  : 0;
              const valueB =
                typeof b['DeepSP广告销售额在SP中占比'] === 'string'
                  ? b['DeepSP广告销售额在SP中占比'].replace('%', '')
                  : 0;
              return Number(valueA) - Number(valueB);
            },
            render: (text: string, record: any) => {
              return (
                <CurrencySymbol
                  value={text || '0%'}
                  oldValue={record.old_DeepSP广告销售额在SP中占比 || '0%'}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: 'Deep_SP销量',
            dataIndex: 'Deep_SP销量',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.aiOrderCount')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.aiOrderCount')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.aiOrderCount')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 135,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseInt(a.Deep_SP销量 || 0) - Number.parseInt(b.Deep_SP销量 || 0);
            },
            checked: true,
            render: (_, record) => {
              return (
                <CurrencySymbol
                  isInteger={true}
                  value={Number.parseInt(record.Deep_SP销量 || 0).toFixed(0)}
                  oldValue={Number.parseInt(record.old_Deep_SP销量 || 0).toFixed(0)}
                  isShowContrast={isShowContrast}
                />
              );
            }
          },
          {
            key: 'SP关键词数量',
            showSorterTooltip: false,
            dataIndex: 'SP关键词数量',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.keywordSeedCount')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.keywordSeedCount')}</li>
                      <li className="flex items-center text-primary">
                        <Icon
                          icon="mingcute:ai-line"
                          className="mr-1"
                        />
                        {t('page.ailisting.tooltips.aiExploreKeywords')}
                      </li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.keywordSeedCount')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 170,
            checked: true,
            sorter: (a: any, b: any) => {
              return Number.parseInt(a.SP关键词数量 || 0) - Number.parseInt(b.SP关键词数量 || 0);
            },
            render: (text: string, record: any) => {
              // 两行显示
              return (
                <div className="flex flex-col items-center gap-1">
                  <CurrencySymbol
                    isShowContrast={false}
                    isInteger={true}
                    value={Number.parseInt(text || 0).toFixed(0)}
                    oldValue={Number.parseInt(record.old_SP关键词数量 || 0).toFixed(0)}
                  />
                  <Suspense>
                    <FunnelChartIcon
                      countryCode={form.getFieldValue('country')}
                      data={{
                        manual: {
                          exposure: record.deep_impressions_manual_sp || 0,
                          clicks: record.deep_clicks_manual_sp || 0,
                          orders: record.Deep_manual_SP销量 || 0,
                          amount: record.Deep_manual_SP销售额 || 0,
                          cost: record.deep_cost_manual_sp || 0,
                          previousData: {
                            exposure: record.old_deep_impressions_manual_sp || 0,
                            clicks: record.old_deep_clicks_manual_sp || 0,
                            orders: record.old_Deep_manual_SP销量 || 0,
                            amount: record.old_Deep_manual_SP销售额 || 0,
                            cost: record.old_deep_cost_manual_sp || 0
                          }
                        },
                        auto: {
                          exposure: record.deep_impressions_auto_sp || 0,
                          clicks: record.deep_clicks_auto_sp || 0,
                          orders: record.Deep_auto_SP销量 || 0,
                          amount: record.Deep_auto_SP销售额 || 0,
                          cost: record.deep_cost_auto_sp || 0,
                          previousData: {
                            exposure: record.old_deep_impressions_auto_sp || 0,
                            clicks: record.old_deep_clicks_auto_sp || 0,
                            orders: record.old_Deep_auto_SP销量 || 0,
                            amount: record.old_Deep_auto_SP销售额 || 0,
                            cost: record.old_deep_cost_auto_sp || 0
                          }
                        },
                        asin: {
                          exposure: record.deep_impressions_asin_sp || 0,
                          clicks: record.deep_clicks_asin_sp || 0,
                          orders: record.Deep_asin_SP销量 || 0,
                          amount: record.Deep_asin_SP销售额 || 0,
                          cost: record.deep_cost_asin_sp || 0,
                          previousData: {
                            exposure: record.old_deep_impressions_asin_sp || 0,
                            clicks: record.old_deep_clicks_asin_sp || 0,
                            orders: record.old_Deep_asin_SP销量 || 0,
                            amount: record.old_Deep_asin_SP销售额 || 0,
                            cost: record.old_deep_cost_asin_sp || 0
                          }
                        }
                      }}
                    />
                  </Suspense>
                </div>
              );
            }
          },
          {
            key: 'SP_ASIN数量',
            showSorterTooltip: false,
            dataIndex: 'SP_ASIN数量',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.asinFlowSeedCount')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.asinFlowSeedCount')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.asinFlowSeedCount')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 180,
            checked: true,
            sorter: (a: any, b: any) => {
              return Number.parseInt(a.SP_ASIN数量 || 0) - Number.parseInt(b.SP_ASIN数量 || 0);
            },
            render: (text: string, record: any) => {
              return (
                <div className="flex flex-col items-center gap-1">
                  <CurrencySymbol
                    isShowContrast={false}
                    isInteger={true}
                    value={Number.parseInt(text || 0).toFixed(0)}
                    oldValue={Number.parseInt(record.old_SP_ASIN数量 || 0).toFixed(0)}
                  />
                  <Suspense>
                    <CompetitorStrategyConfig
                      countryCode={form.getFieldValue('country')}
                      pAsin={record.parent_asin}
                    />
                  </Suspense>
                </div>
              );
            }
          },
          {
            key: 'ACOS',
            dataIndex: 'ACOS',
            showSorterTooltip: false,
            title: () => (
              <span>
                <Popover
                  title="ACOS"
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.acos')}</li>
                    </ul>
                  }
                >
                  {'ACOS'}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 90,
            sorter: (a: any, b: any) => {
              const valueA = typeof a.ACOS === 'string' ? a.ACOS.replace('%', '') : 0;
              const valueB = typeof b.ACOS === 'string' ? b.ACOS.replace('%', '') : 0;
              return Number(valueA) - Number(valueB);
            },
            checked: true,
            render: (text: string, record: any) => {
              const spend = new BigNumber(record.广告总花费 || 0);
              const sales = new BigNumber(record.广告总销售额 || 0);
              const acos = `${spend.dividedBy(sales).multipliedBy(100).toFixed(2)}%`;
              // 计算old
              const oldAcos = `${new BigNumber(record.old_广告总花费 || 0)
                .dividedBy(new BigNumber(record.old_广告总销售额 || 0))
                .multipliedBy(100)
                .toFixed(2)}%`;
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={acos}
                  oldValue={oldAcos}
                  isShowContrast={isShowContrast}
                  reverseColors={true}
                />
              );
            }
          },

          {
            key: '日预算',
            dataIndex: '日预算',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.aiDailyBudget')}
                  placement="top"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.aiDailyBudget')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.aiDailyBudget')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 140,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseFloat(a.日预算 || 0) - Number.parseFloat(b.日预算 || 0);
            },
            checked: false,
            render: (_, record) => {
              // 保留整数
              return (
                <CurrencySymbol
                  countryCode={form.getFieldValue('country')}
                  value={Number.parseFloat(record.日预算 || 0)}
                  isShowContrast={false}
                />
              );
              // return <span>{record.日预算 || 0}</span>
            }
          },
          {
            key: 'ad_num',
            dataIndex: 'ad_num',
            title: t('page.ailisting.columns.aiCampaignCount'),
            align: 'center',
            width: 150,
            showSorterTooltip: false,
            sorter: (a: any, b: any) => {
              return Number.parseInt(a.ad_num || 0) - Number.parseInt(b.ad_num || 0);
            },
            checked: false,
            render: (_, record) => {
              // 保留整数
              return <span>{record.ad_num || 0}</span>;
            }
          },
          {
            key: 'PreAcos',
            showSorterTooltip: false,
            dataIndex: 'PreAcos',
            title: () => (
              <span>
                <Popover
                  title={t('page.ailisting.columns.targetAcos')}
                  placement="topLeft"
                  content={
                    <ul>
                      <li>{t('page.ailisting.tooltips.targetAcos')}</li>
                      <li>{t('page.ailisting.tooltips.targetAcosStrategy')}</li>
                    </ul>
                  }
                >
                  {t('page.ailisting.columns.targetAcos')}
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 130,
            fixed: 'right',
            checked: true,
            sorter: (a: any, b: any) => {
              return a.PreAcos - b.PreAcos;
            },
            render: (_, record) => {
              const preAcosMap = {
                '16': t('page.ailisting.acosStrategy.ultraConservative'),
                '20': t('page.ailisting.acosStrategy.moderatelyConservative'),
                '24': t('page.ailisting.acosStrategy.conservative'),
                '28': t('page.ailisting.acosStrategy.balanced'),
                '32': t('page.ailisting.acosStrategy.aggressive'),
                '50': t('page.ailisting.acosStrategy.ultraAggressive')
              };
              const label = preAcosMap[String(record.PreAcos)];
              if (label) {
                return <ATag>{label}</ATag>;
              }
              return (
                <ATag color="warning">
                  {`${t('page.ailisting.acosStrategy.custom')} ${getStrategyLabel({ acosValue: Number(record.PreAcos), options: asinOptions, selectedAcos: record.PreAcos })}`}
                </ATag>
              );
            }
          },
          // ManageState
          // { label: '审核中', value: 0 },
          // { label: "准备中", value: 1 },
          // { label: '投放中', value: 2 },
          // // 3 已经取消(停止)授权
          // { label: '已经取消授权', value: 3 },
          // { label: '暂停中', value: 4 },
          // { label: '已暂停', value: 5 },
          // // 6 取消中不选不要传递
          // { label: '取消中', value: 6 }
          {
            key: 'ManageState',
            showSorterTooltip: false,
            dataIndex: 'ManageState',
            // title: '状态',
            title: () => (
              <span>
                {t('page.ailisting.columns.status')}
                <Popover
                  title={t('page.ailisting.tooltips.statusExplanation')}
                  placement="topLeft"
                  content={
                    <ul>
                      {/* <li>审核中：审核中，请耐心等待</li> */}
                      <li className="mb-2">
                        <ATag color="blue">{t('page.ailisting.status.preparing')}</ATag>{' '}
                        {t('page.ailisting.statusDescriptions.preparing')}
                      </li>
                      <li className="mb-2">
                        <ATag color="green">{t('page.ailisting.status.running')}</ATag>{' '}
                        {t('page.ailisting.statusDescriptions.running')}
                      </li>
                      <li className="mb-2">
                        <ATag color="volcano">{t('page.ailisting.status.pausing')}</ATag>{' '}
                        {t('page.ailisting.statusDescriptions.pausing')}
                      </li>
                      <li className="mb-2">
                        <ATag color="orange">{t('page.ailisting.status.paused')}</ATag>{' '}
                        {t('page.ailisting.statusDescriptions.paused')}
                      </li>
                      <li className="mb-2">
                        <ATag color="orange">{t('page.ailisting.status.cancelling')}</ATag>{' '}
                        {t('page.ailisting.statusDescriptions.cancelling')}
                      </li>
                      <li>
                        <ATag color="purple">{t('page.ailisting.status.recovering')}</ATag>{' '}
                        {t('page.ailisting.statusDescriptions.recovering')}
                      </li>
                      {/* <li>已取消授权: 已经取消授权，不会对Listing进行任何广告操作</li> */}
                    </ul>
                  }
                >
                  <QuestionCircleOutlined className="ml-1" />
                </Popover>
              </span>
            ),
            align: 'center',
            width: 90,
            fixed: 'right',
            checked: true,
            sorter: (a: any, b: any) => {
              return a.ManageState - b.ManageState;
            },
            render: (_, record) => {
              // 0 申请托管 1 准备中 2 运行中 3 已经取消授权  4 申请取消授权  5取消中 6申请暂停 7 暂停中 8 已经暂停 9 申请恢复， a 恢复中
              // 状态映射
              const manageStateMap = {
                '0': { color: 'blue', label: t('page.ailisting.status.preparing') },
                '1': { color: 'blue', label: t('page.ailisting.status.preparing') },
                '2': { color: 'green', label: t('page.ailisting.status.running') },
                '3': { color: 'red', label: t('page.ailisting.status.cancelled') },
                '4': { color: 'orange', label: t('page.ailisting.status.cancelling') },
                '5': { color: 'orange', label: t('page.ailisting.status.cancelling') },
                '6': { color: 'volcano', label: t('page.ailisting.status.pausing') },
                '7': { color: 'volcano', label: t('page.ailisting.status.pausing') },
                '8': { color: 'volcano', label: t('page.ailisting.status.paused') },
                '9': { color: 'purple', label: t('page.ailisting.status.recovering') },
                a: { color: 'purple', label: t('page.ailisting.status.recovering') }
              };
              const state = manageStateMap[record.ManageState];
              return <ATag color={state.color}>{state.label}</ATag>;
            }
          },
          {
            key: '操作',
            dataIndex: '操作',
            title: t('page.ailisting.columns.operations'),
            align: 'center',
            fixed: 'right',
            checked: true,
            width: 200,
            // width: 100,
            render: (_, record) => {
              return (
                <Suspense>
                  <div className="flex flex-col items-start gap-1">
                    <AButton
                      type="link"
                      onClick={() => {
                        navigate(
                          `/ai/camtar-listing?market=${form.getFieldValue('country')}&asin=${record.parent_asin}&isAuth=${record.IsAuth}`,
                          '_blank'
                        );
                      }}
                      size="small"
                    >
                      <div className="flex items-center text-sm">
                        <Icon
                          icon="fluent:apps-list-detail-24-regular"
                          className="mr-1"
                        />
                        {t('page.ailisting.buttons.adCampaignList')}
                      </div>
                    </AButton>
                    <AsinStatusSetter
                      disabled={Number(record.ManageState) != 2}
                      parentAsin={record.parent_asin}
                      countryCode={form.getFieldValue('country')}
                    ></AsinStatusSetter>
                    <ADropdown menu={{ items: createMenuItems(record) }}>
                      <AButton
                        type="link"
                        size="small"
                      >
                        <p className="flex items-center">
                          <Icon
                            icon="carbon:batch-job"
                            className="mr-1 text-lg"
                          />
                          {t('page.ailisting.buttons.ASINManagement')}
                        </p>
                      </AButton>
                    </ADropdown>
                  </div>
                </Suspense>
              );
            }
          }
        ];
        // 应用存储的列设置
        const storedKeys = getStoredColumnKeys();
        if (storedKeys) {
          defaultColumns.forEach(column => {
            if (column.key) {
              // 如果key在存储的列表中，则设置为选中
              column.checked = storedKeys.includes(column.key);
            }
          });
        }

        return defaultColumns;
      }
    },
    { showQuickJumper: true }
  );

  const {
    checkedRowKeys,
    rowSelection,
    onBatchDeleted,
    onDeleted,
    handleEdit,
    handleAdd,
    drawerVisible,
    closeDrawer,
    operateType,
    editingData,
    clearCheckedRowKeys
  } = useTableOperate(data, run);

  // 定义asin 状态
  const calculateAsinState = (record: any) => {
    if (record.ManageState == 0 || record.ManageState == 1) return 0; // 审核中
    let acos = 0;
    if (record.Deep_SP_ACOS == 'NaN%' || record.Deep_SP_ACOS == 'None' || !record.Deep_SP_ACOS) {
      acos = 0;
    } else {
      acos = Number.parseFloat(record.Deep_SP_ACOS.replace('%', '')) || 0;
    }

    if (record.HaveNum < asinLimitStore) return 1;
    if (acos == 0) return 0; // 0 不计算
    const keywordNum = record.SP关键词数量 || 0;
    // console.log(acos, "acos")
    let defaultAcosValue = 0;
    switch (shopPreAcos) {
      case 0:
        defaultAcosValue = 20;
        break;
      case 1:
        defaultAcosValue = 24;
        break;
      case 2:
        defaultAcosValue = 28;
        break;
      case 3:
        defaultAcosValue = 32;
        break;
      case 4:
        defaultAcosValue = 32;
        break;
    }
    // console.log(ShopPreAcos, "ShopPreAcos==defaultAcosValue")
    // console.log(defaultAcosValue, "defaultAcosValue")
    if (keywordNum >= 150) {
      // console.log(acos, "acos")
      return acos <= defaultAcosValue ? 5 : 3; // 2: Below Target, 3: Above Target
    }
    return acos <= defaultAcosValue ? 4 : 2; // 4: Below Target, 5: Above Target
  };

  // 定义asin 颜色
  const getColor = (asin_state: number) => {
    switch (asin_state) {
      case 0:
        return '#FFFFFF'; // light gray
      case 1:
        return '#ccc'; // light gray
      case 2:
        return '#F44336'; // light red
      case 3:
        return '#FFA726'; // light blue
      case 4:
        return '#1664ff'; // light yellow
      case 5:
        return '#66BB6A'; // light green
    }
  };

  async function handleBatchDelete() {
    // request
    console.log(checkedRowKeys);
    onBatchDeleted();
  }

  const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
    if (info.source === 'trigger' || nextOpen) {
      setOpen(nextOpen);
    }
  };

  function handleDelete(id: number) {
    // request
    console.log(id);

    onDeleted();
  }

  function edit(id: number) {
    handleEdit(id);
  }

  // 设置特定ASIN的ACOS
  const handleSetAcos = async (Acos: string | BigNumber) => {
    try {
      const asins = operationKeysRef.current.map((key: any) => {
        const item: any = tableProps.dataSource.find((data: any) => data.ID === key);
        return item.Asin;
      });
      const res = await AuthAsinSetShopPreAcos({
        AsinAcos: Acos,
        CountryCode: form.getFieldValue('country'),
        Asin: asins.join(',')
      });
      console.log(res, 'res====');
      if (res.data) {
        window.$message?.success(t('page.ailisting.messages.settingSuccess'));
        handleBatchDelete();
        acosModalRef.current?.close();
      }
    } catch (error) {
      console.error('设置ACOS失败:', error);
      window.$message?.error(t('page.ailisting.messages.settingFailed'));
    }
  };

  const handleCancelAsin = async (operationIds: any[], singleItemId?: number) => {
    // 2 和 8
    const allManageStateIsSix = operationIds.every((key: any) => {
      const item: any = tableProps.dataSource.find((data: any) => data.ID === key);
      return item && (item.ManageState == '2' || item.ManageState == '8');
    });
    if (!allManageStateIsSix) {
      window.$message?.warning(t('page.ailisting.messages.selectRunningOrPausedOnly'));
      return;
    }
    const res = await AuthAsinCancel({ ID: operationIds.join(','), CountryCode: form.getFieldValue('country') });
    if (res.data) {
      // 取消成功
      window.$message?.success(t('page.ailisting.messages.cancelSuccess'));
      if (!singleItemId) handleBatchDelete();
      else run();
    }
  };

  // Helper function to stop ASIN operation
  const handleStopAsin = async (operationIds: any[], singleItemId?: number) => {
    const allManageStateIsSix = operationIds.every((key: any) => {
      const item: any = tableProps.dataSource.find((data: any) => data.ID === key);
      return item && item.ManageState == '2';
    });

    if (!allManageStateIsSix) {
      window.$message?.warning(t('page.ailisting.messages.selectRunningOnly'));
      return;
    }
    const res = await AuthAsinOperate({
      ManageState: 6,
      ID: operationIds.join(','),
      CountryCode: form.getFieldValue('country')
    });

    if (res.data) {
      window.$message?.success(t('page.ailisting.messages.pauseSuccess'));
      if (!singleItemId) handleBatchDelete();
      else run();
    }
  };

  // Helper function to resume ASIN operation
  const handleResumeAsin = async (operationIds: any[], singleItemId?: number) => {
    const allManageStateIsSix = operationIds.every((key: any) => {
      const item: any = tableProps.dataSource.find((data: any) => data.ID === key);
      return item && item.ManageState === '8';
    });

    if (allManageStateIsSix) {
      const res = await AuthAsinOperate({
        ManageState: 9,
        ID: operationIds.join(','),
        CountryCode: form.getFieldValue('country')
      });

      if (res.data) {
        window.$message?.success(t('page.ailisting.messages.resumeSuccess'));
        if (!singleItemId) handleBatchDelete();
        else run();
      }
    } else {
      window.$message?.warning(t('page.ailisting.messages.selectPausedOnly'));
    }
  };

  const handleBatchOperate = async (_api: string | Function, status?: string, singleItemId?: number) => {
    console.log('批量操作聚合', _api, status);
    // Use singleItemId if provided, otherwise use checkedRowKeys
    const operationIds = singleItemId ? [singleItemId] : checkedRowKeys;

    switch (_api) {
      case 'AuthAsinSetShopPreAcos':
        // Store current operation keys for ACOS setting
        operationKeysRef.current = operationIds;
        // 打开modal
        acosModalRef.current?.open();
        break;

      case 'AuthAsinCancel':
        await handleCancelAsin(operationIds, singleItemId);
        break;

      case 'AuthAsinOperate':
        if (status === 'stop') {
          await handleStopAsin(operationIds, singleItemId);
        } else {
          await handleResumeAsin(operationIds, singleItemId);
        }
        break;

      case 'AuthAsinDiagnosis':
        aiDiagnosisModalRef.current?.open();
        break;

      default:
        break;
    }
  };

  const createMenuItems = (record?: any) => [
    {
      key: 'setAcos',
      label: (
        <p className="flex items-center">
          {' '}
          <SettingOutlined className="mr-1" /> {t('page.ailisting.dropdownMenu.setAcos')}
        </p>
      ),
      onClick: () =>
        record
          ? handleBatchOperate('AuthAsinSetShopPreAcos', undefined, record.ID)
          : handleBatchOperate('AuthAsinSetShopPreAcos')
    },
    {
      key: 'cancelAuth',
      label: (
        <APopconfirm
          placement="bottom"
          title={t('page.ailisting.confirmDialogs.cancelHosting.title')}
          description={t('page.ailisting.confirmDialogs.cancelHosting.description')}
          onConfirm={() =>
            record ? handleBatchOperate('AuthAsinCancel', undefined, record.ID) : handleBatchOperate('AuthAsinCancel')
          }
        >
          <p className="w-full flex items-center">
            {' '}
            <CloseCircleOutlined className="mr-1" /> {t('page.ailisting.dropdownMenu.cancelHosting')}
          </p>
        </APopconfirm>
      )
    },
    {
      key: 'pauseAuth',
      label: (
        <APopconfirm
          placement="bottom"
          title={t('page.ailisting.confirmDialogs.pauseDelivery.title')}
          description={t('page.ailisting.confirmDialogs.pauseDelivery.description')}
          onConfirm={() =>
            record
              ? handleBatchOperate('AuthAsinOperate', 'stop', record.ID)
              : handleBatchOperate('AuthAsinOperate', 'stop')
          }
        >
          <p className="w-full flex items-center">
            {' '}
            <PauseCircleOutlined className="mr-1" /> {t('page.ailisting.dropdownMenu.pauseDelivery')}
          </p>
        </APopconfirm>
      )
    },
    {
      key: 'resumeAuth',
      label: (
        <p className="w-full flex items-center">
          {' '}
          <PlayCircleOutlined className="mr-1" /> {t('page.ailisting.dropdownMenu.resumeDelivery')}
        </p>
      ),
      onClick: () =>
        record ? handleBatchOperate('AuthAsinOperate', undefined, record.ID) : handleBatchOperate('AuthAsinOperate')
    }
  ];

  const menuItems = createMenuItems();
  // 头部操作插槽
  const [timeBudgetModalVisible, setTimeBudgetModalVisible] = useState(false);
  const prefix = () => {
    return (
      <div className="flex items-center">
        <AButton
          // disabled={checkedRowKeys.length === 0}
          loading={tableProps.loading}
          type="primary"
          ghost
          size="small"
          className="mr-2"
          onClick={() => setTimeBudgetModalVisible(true)}
        >
          <p className="flex items-center">
            <Icon
              icon="streamline-freehand:presentation-projector-screen-budget-analytics"
              className="mr-1 text-lg"
            />
            {t('page.ailisting.buttons.timeBudget')}
          </p>
        </AButton>
        {/* AI诊断Listing */}
        <AButton
          type="primary"
          ghost
          size="small"
          className="mr-2"
          onClick={() => handleBatchOperate('AuthAsinDiagnosis')}
        >
          <div className="flex items-center">
            <Icon
              icon="ri:dvd-ai-line"
              className="mr-1 text-lg"
            />
            {t('page.ailisting.buttons.aiDiagnosis')}
          </div>
        </AButton>
        <AButton
          disabled={checkedRowKeys.length === 0}
          type="primary"
          ghost
          size="small"
          className="mr-2"
          onClick={() => handleBatchOperate('AuthAsinSetShopPreAcos')}
        >
          <p className="flex items-center">
            {/* <SettingOutlined className='mr-1' /> */}
            <Icon
              icon="line-md:cog-filled"
              className="mr-1 text-lg"
            />
            {t('page.ailisting.buttons.setTargetAcos')}
          </p>
        </AButton>

        {/* 批量操作 */}
        <ADropdown
          menu={{ items: menuItems }}
          disabled={checkedRowKeys.length === 0}
          // onOpenChange={handleOpenChange}
          // open={open && !tableProps.loading}
        >
          <AButton
            type="primary"
            ghost
            size="small"
          >
            <p className="flex items-center">
              <Icon
                icon="carbon:batch-job"
                className="mr-1 text-lg"
              />
              {t('page.ailisting.buttons.batchOperations')}
              {/* <DownOutlined /> */}
            </p>
          </AButton>
        </ADropdown>
      </div>
    );
  };

  const getTableData = async (params: any = {}) => {
    // console.log(params, "params====")
    clearCheckedRowKeys();
    run(params);
    getShopPreAcos();
  };
  // 获取全店预期ACOS
  const getShopPreAcos = async () => {
    const res = await AuthAsinGetUpdate({
      CountryCode: form.getFieldValue('country')
    });
    setShopPreAcos(res.data.ShopPreAcos || 0);
    setAsinLimitStore(res.data.AsinLimitStore || 0);
  };

  const filteredData = () => {
    const filteredData = tableProps.dataSource.map((item: any) => {
      // 增加asin_state
      const asin_state = calculateAsinState(item);
      return {
        ...item,
        asin_state
      };
    });
    // 按照asin_state 排序
    filteredData.sort((a: any, b: any) => b.asin_state - a.asin_state);
    return filteredData;
  };

  // console.log(columnChecks,"columnChecks====")
  return (
    <div className="max-h-1500px min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      <Suspense>
        <TimeBudgetModal
          visible={timeBudgetModalVisible}
          onClose={() => setTimeBudgetModalVisible(false)}
          onSuccess={() => {
            getTableData({});
          }}
          selectedAsins={checkedRowKeys.map((key: any) => {
            const item: any = tableProps.dataSource.find((data: any) => data.ID === key);
            return item.Asin;
          })}
          countryCode={form.getFieldValue('country')}
        />
      </Suspense>
      <Suspense>
        <AiDiagnosisModal
          ref={aiDiagnosisModalRef}
          countryCode={form.getFieldValue('country')}
        />
      </Suspense>
      <ACard>
        <UserSearch
          search={getTableData}
          reset={reset}
          form={form}
          loading={tableProps.loading}
        />
      </ACard>
      <div className="flex items-center justify-between px-1">
        <div className="flex items-center">
          <AButton
            // icon={}
            size="small"
            ghost
            type="primary"
            className="mr-2"
            onClick={handleAdd}
          >
            <p className="flex items-center">
              <Icon
                icon="ic:round-plus"
                className="mr-1 text-icon"
              />
              {t('page.ailisting.buttons.addHostedAsin')}
            </p>
          </AButton>
          {/* 设置整体ACOS */}
          <SelectAcos
            reset={() => {
              getShopPreAcos();
              reset();
            }}
            shopPreAcos={shopPreAcos}
            form={form}
            loading={tableProps.loading}
          />
        </div>

        <DocumentButton
          text={t('page.ailisting.buttons.hostingGuide')}
          link="https://deepthought.feishu.cn/wiki/GuENwuUlxi2UNBk97x6coMupn2b"
        />
        {/* 添加授权ASIN */}

        {/* 设置特定ASIN的ACOS */}
        <AcosSettingModal
          ref={acosModalRef}
          onSuccess={handleSetAcos}
          initialAcos={shopPreAcos}
        />
      </div>

      <ACard
        ref={tableWrapperRef}
        bordered={false}
        extra={
          <TableHeaderOperation
            onDelete={handleBatchDelete}
            refresh={() => {
              getTableData({});
            }}
            prefix={prefix()}
            add={handleAdd}
            loading={tableProps.loading}
            setColumnChecks={handleSetColumnChecks}
            disabledDelete={checkedRowKeys.length === 0}
            columns={columnChecks}
          />
        }
        title={
          <div className="max-w-200px">
            {t('page.ailisting.title')}
            <ColorExplanation />
          </div>
        }
        className="flex-col-stretch sm:flex-1-hidden card-wrapper"
      >
        <ATable
          scroll={{
            y: 700,
            x: 702
          }}
          rowSelection={rowSelection}
          size="small"
          {...tableProps}
          dataSource={filteredData()}
          pagination={{
            ...tableProps.pagination,
            showTotal: (total, range) => t('page.listingall.pagination.total', { total })
          }}
          locale={{
            emptyText: (
              <AEmpty
                image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                // loading={tableProps.loading}
                description={
                  !tableProps.loading && (
                    <div className="flex-col items-center">
                      <AButton
                        type="primary"
                        className="my-2"
                        onClick={() => reset()}
                      >
                        {t('page.ailisting.buttons.refreshPage')}
                      </AButton>
                      <AButton
                        className="mt-1"
                        type="link"
                        onClick={() => {
                          // window.location.href = '/ai/listingall';
                          navigate('/ai/listingall');
                        }}
                      >
                        {t('page.ailisting.buttons.goToListingPage')}
                      </AButton>
                    </div>
                  )
                }
              ></AEmpty>
            )
          }}
          // tableLayout="auto"
          //   dataSource={formattedData}
        />
        <Suspense>
          <NewAntdModalComponent
            visible={drawerVisible}
            closeDrawer={closeDrawer}
            form={form}
            loading={tableProps.loading}
            onRefresh={run}
          />
        </Suspense>
      </ACard>
    </div>
  );
}
