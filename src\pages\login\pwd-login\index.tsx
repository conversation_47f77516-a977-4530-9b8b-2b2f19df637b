import { Button, Form, Input, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { useRef, useState } from 'react';
// import { useKeyPress } from '@/hooks/common/useKeyPress';
import { loginModuleRecord } from '@/constants/app';
import { useLogin } from '@/hooks/common/login';
// import { useFormRules } from '@/hooks/common/formRules';
import { useRouterPush } from '@/hooks/common/routerPush';
import { localStg } from '@/utils/storage';
import CaptchaQRCode from '../captch-qr-code';

type LoginParams = {
  email: string;
  password: string;
  captchaKey?: string;
  captchaValue?: string;
};

export function Component() {
  const [form] = Form.useForm<LoginParams>();
  const { toggleLoginModule } = useRouterPush();
  const { t } = useTranslation();
  const { loading, toLogin } = useLogin();
  const captchaRef = useRef(null);
  const [searchParams] = useSearchParams();
  const [captchaKey, setCaptchaKey] = useState(''); // 保存验证码键
  const {
    formRules: { email, pwd }
  } = useFormRules();

  async function handleSubmit() {
    const params = await form.validateFields();

    // 检查是否是销售演示账号
    if (params.email === '<EMAIL>') {
      console.log('检测到销售演示账号登录');
      // 使用演示模式登录 - 创建特殊的用户信息
      const demoUserInfo = {
        not_read_msg_count: 66,
        OwnerID: 8008208820,
        ShopName: '👋你好，演示店',
        OwnerFlag: 0,
        NickName: 'xiaoshouyanshi',
        Email: '<EMAIL>',
        Phone: '18888888888',
        CompanyName: 'DeepBI',
        Balance: 45755.66,
        demoMode: true,
        GiftBalance: 0,
        SumBalance: 0,
        group_shop: {
          8008208821: {
            ID: 8008208821,
            ShopName: '👋你好，演示店',
            IsDistributor: false,
            RegDatetime: '2025-01-23 15:35:53'
          }
        },
        active_shop_id: '8008208821',
        IsDistributor: false,
        active_shop_regdatetime: '2025-01-23 15:35:53',
        is_first_login: 0,
        alert_country: { alert_country_3: [], alert_country_7: [] },
        roles: ['R_ADMIN']
      };

      // 生成随机token
      const randomString = Math.random().toString(36).substring(2, 20);

      // 存储token和用户信息
      localStg.set('token', randomString);
      localStg.set('refreshToken', randomString);
      localStg.set('userInfo', demoUserInfo);

      // 创建一个特殊的登录参数对象
      const loginParams = {
        demoMode: true,
        token: randomString,
        userInfo: demoUserInfo
      };

      // 使用特殊参数登录
      toLogin(loginParams, captchaRef);
      return;
    }

    // 正常登录流程
    const isEmail = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(params.email);
    const loginParams = isEmail
      ? { email: params.email, password: params.password, captchaValue: params.captchaValue, captchaKey, lang: 'zh' }
      : { phone: params.email, password: params.password, captchaValue: params.captchaValue, captchaKey, lang: 'zh' };

    console.log(loginParams, 'loginParams==');
    console.log(params, 'params===');
    toLogin(loginParams, captchaRef);
  }

  useKeyPress('enter', () => {
    handleSubmit();
  });

  function handleAccountLogin(account: Account) {
    // toLogin(account);
  }

  // useEffect(() => {
  //  console.log(searchParams,'guanliyuan======')
  // }, [searchParams])

  return (
    <>
      <h3 className="text-18px text-primary font-medium">{t('page.login.pwdLogin.title')}</h3>
      <Form
        className="pt-24px"
        form={form}
        initialValues={{
          email: '',
          Password: ''
        }}
      >
        <Form.Item
          // rules={email}
          // 如果有@，则认为是邮箱，否则认为是手机号
          rules={[
            {
              validator: (_, value) => {
                if (!value) {
                  return Promise.reject('请输入邮箱或手机号');
                }
                const isEmail = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value);
                const isPhone = /^1[3-9]\d{9}$/.test(value);
                if (isEmail || isPhone) {
                  return Promise.resolve();
                }
                return Promise.reject('请输入正确的邮箱或手机号');
              }
            }
          ]}
          name="email"
        >
          <Input placeholder="请输入邮箱/手机号" />
        </Form.Item>

        <Form.Item
          rules={pwd}
          name="password"
        >
          <Input.Password
            autoComplete="password"
            placeholder="请输入密码"
          />
        </Form.Item>
        <Space
          direction="vertical"
          className="w-full"
          size={24}
        >
          <CaptchaQRCode
            ref={captchaRef}
            onCaptchaChange={(value, key) => {
              setCaptchaKey(key);
            }}
          />
          <div className="flex-y-center justify-end">
            {/* <Checkbox>{t('page.login.pwdLogin.rememberMe')}</Checkbox> */}

            <Button
              type="text"
              className="mt-[-24px]"
              onClick={() => toggleLoginModule('reset-pwd')}
            >
              {t('page.login.pwdLogin.forgetPassword')}
            </Button>
          </div>
          <Button
            type="primary"
            size="large"
            loading={loading}
            shape="round"
            onClick={handleSubmit}
            block
          >
            {t('common.confirm')}
          </Button>
          <div className="flex-y-center justify-between gap-12px">
            <Button
              block
              className="flex-1"
              onClick={() => toggleLoginModule('code-login')}
            >
              {t(loginModuleRecord['code-login'])}
            </Button>
            <Button
              block
              className="flex-1"
              onClick={() => toggleLoginModule('register')}
            >
              {t(loginModuleRecord.register)}
            </Button>
          </div>
        </Space>
      </Form>
    </>
  );
}
