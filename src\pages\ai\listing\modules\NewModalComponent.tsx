import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, Input, InputNumber, Modal } from 'antd';

import { AuthAsinAdd, AuthAsinCheck } from '@/service/api';
interface NewAntdModalComponentProps {
  visible: boolean;
  closeDrawer: () => void;
  form: any;
  onRefresh: () => void;
  tableloading: boolean;
}

interface AsinData {
  Asin: string;
  PreAcos: number;
  AuthMaxMoney: number;
}

const NewAntdModalComponent: React.FC<NewAntdModalComponentProps> = ({
  visible,
  closeDrawer,
  form,
  onRefresh,
  tableloading
}) => {
  const { t } = useTranslation();
  const [step, setStep] = useState<number>(1);
  const [newAuthAsin, setNewAuthAsin] = useState<AsinData[]>([]);
  const [errorAsinText, setErrorAsinText] = useState<string>('');
  const [newAuthAsinText, setNewAuthAsinText] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);

  const handleSubmitStep2 = async (values: AsinData[]) => {
    setLoading(true);
    try {
      const res = await AuthAsinAdd({ AsinData: values, CountryCode: form.getFieldValue('country') });
      if (res.data) {
        window.$notification?.success({
          message: t('page.ailisting.newAsinModal.messages.addSuccess')
        });
        closeDrawer();
        setLoading(false);
        onRefresh();
      } else {
        const asin_data = [...res.data.failed, ...res.data.error].join(',');
        window.$notification?.error({
          message: `${t('page.ailisting.newAsinModal.messages.addFailed')}: ${asin_data}`
        });
        setLoading(false);
      }
    } catch {
      // Ignore error details, just handle the error state
      setLoading(false);
    }
  };

  const handleSubmitStep1 = async (values: any) => {
    if (tableloading) {
      return;
    }
    setLoading(true);
    let asin_data = values.asinTextArea || '';
    if (values.newAuthAsinTextArea) {
      asin_data += `\n${values.newAuthAsinTextArea}`;
    }
    if (values.errorAsinTextArea) {
      asin_data += `\n${values.errorAsinTextArea}`;
    }
    try {
      const errMsg = [];
      const res = await AuthAsinCheck({ asin_data, CountryCode: form.getFieldValue('country') });
      if (res.data) {
        if (!res.data.have_all_asin) {
          window.$notification?.warning({
            message: t('page.ailisting.newAsinModal.messages.dataCollecting'),
            description: (
              <div>
                {t('page.ailisting.newAsinModal.messages.checkMessagePage')}
                <span
                  className="cursor-pointer text-blue-500"
                  onClick={() => {
                    window.location.href = '/message';
                  }}
                >
                  {t('page.ailisting.newAsinModal.messages.messagePage')}
                </span>
                {t('page.ailisting.newAsinModal.messages.checkDetails')}
              </div>
            )
          });
          setLoading(false);
          return;
        }

        if (res.data.new_auth_asin.length === 0) {
          // Check for authorized ASINs
          if (res.data.authed_asin.length > 0) {
            errMsg.push(
              `${t('page.ailisting.newAsinModal.messages.asinAuthorized')}: ${res.data.authed_asin.join(', ')}`
            );
          }

          // Check for non-existent ASINs
          if (res.data.not_exsit_asin.length > 0) {
            errMsg.push(
              `${t('page.ailisting.newAsinModal.messages.asinNotExist')}: ${res.data.not_exsit_asin.join(', ')}`
            );
          }
          if (res.data.storage_not_enough.length > 0) {
            errMsg.push(
              `${t('page.ailisting.newAsinModal.messages.insufficientInventory')}${res.data.AsinLimitStore}：${res.data.storage_not_enough.join(', ')}`
            );
          }
          if (res.data.processing_asin.length > 0) {
            errMsg.push(
              `${t('page.ailisting.newAsinModal.messages.asinProcessing')}: ${res.data.processing_asin.join(', ')}`
            );
          }

          // Display the error message if there are any
          if (errMsg.length > 0) {
            window.$notification?.error({
              message: (
                <div>
                  {errMsg.map((msg, index) => (
                    <div key={index}>{msg}</div>
                  ))}
                </div>
              )
            });
            setLoading(false);
            return;
          }
        }
        const new_auth_asin = res.data.new_auth_asin.map((item: string) => ({
          Asin: item,
          PreAcos: 0.2,
          AuthMaxMoney: 200
        }));
        const error_asin = res.data.error_asin.map((item: string) => ({ Asin: item, PreAcos: 0.2, AuthMaxMoney: 200 }));
        setNewAuthAsinText(res.data.new_auth_asin.join('\n'));
        setErrorAsinText(res.data.error_asin.join('\n'));
        if (error_asin.length === 0) {
          setNewAuthAsin([...new_auth_asin]);
          handleSubmitStep2([...new_auth_asin]);
        } else {
          window.$notification?.error({
            message: t('page.ailisting.newAsinModal.messages.validationFailed')
          });
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    } catch {
      // Ignore error details, just handle the error state
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      setStep(1);
      setNewAuthAsin([]);
      setErrorAsinText('');
      setNewAuthAsinText('');
      if (formRef.current) {
        formRef.current.resetFields();
      }
    }
  }, [visible]);

  return (
    <Modal
      title={`${t(`page.setting.country.${form.getFieldValue('country')}`)} ${t('page.ailisting.newAsinModal.title')}`}
      open={visible}
      onCancel={() => {
        if (!loading) {
          closeDrawer();
        }
      }}
      footer={null}
      width={800}
      maskClosable={false}
    >
      <Form
        ref={formRef}
        onFinish={step === 1 ? handleSubmitStep1 : handleSubmitStep2}
        layout="vertical"
      >
        {step === 1 ? (
          <>
            {!errorAsinText && !newAuthAsinText && (
              <Form.Item
                name="asinTextArea"
                label={t('page.ailisting.newAsinModal.asinOrUrl')}
                rules={[{ required: true, message: t('page.ailisting.newAsinModal.asinRequired') }]}
              >
                <Input.TextArea
                  rows={10}
                  placeholder={t('page.ailisting.newAsinModal.asinPlaceholder')}
                />
              </Form.Item>
            )}
            {errorAsinText && (
              <Form.Item
                name="errorAsinTextArea"
                label={t('page.ailisting.newAsinModal.failedAsins')}
                initialValue={errorAsinText}
              >
                <Input.TextArea rows={5} />
              </Form.Item>
            )}
            {newAuthAsinText && (
              <Form.Item
                name="newAuthAsinTextArea"
                label={t('page.ailisting.newAsinModal.passedAsins')}
                initialValue={newAuthAsinText}
              >
                <Input.TextArea
                  rows={5}
                  readOnly
                />
              </Form.Item>
            )}
            <div className="rounded-md bg-blue-100 p-4 text-blue-900">
              {t('page.ailisting.newAsinModal.noticeTitle')}
              <br />
              {t('page.ailisting.newAsinModal.noticeContent1')}
              <br />
              {t('page.ailisting.newAsinModal.noticeContent2')}
              <br />
              {t('page.ailisting.newAsinModal.noticeContent3')}
              <br />
              {t('page.ailisting.newAsinModal.noticeContent4')}
            </div>
            <div className="w-full flex justify-end">
              <Button
                loading={loading}
                type="primary"
                htmlType="submit"
                className="mt-4"
              >
                {t('page.ailisting.newAsinModal.confirmHosting')}
              </Button>
            </div>
          </>
        ) : (
          <>
            {newAuthAsin.map((asin, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}
              >
                <Form.Item
                  name={['rules', index, 'Asin']}
                  label={t('page.ailisting.newAsinModal.asinOrUrl')}
                  initialValue={asin.Asin}
                  rules={[{ required: true, message: t('page.ailisting.newAsinModal.enterAsin') }]}
                >
                  <Input
                    disabled
                    style={{ width: 220, marginRight: 30 }}
                  />
                </Form.Item>
                <Form.Item
                  name={['rules', index, 'PreAcos']}
                  label={t('page.ailisting.newAsinModal.expectedAcos')}
                  initialValue={asin.PreAcos}
                  rules={[{ required: true, message: t('page.ailisting.newAsinModal.enterAcos') }]}
                >
                  <InputNumber
                    min={0}
                    step={0.2}
                    style={{ width: 120, marginRight: 30 }}
                  />
                </Form.Item>
                <Form.Item
                  name={['rules', index, 'AuthMaxMoney']}
                  label={t('page.ailisting.newAsinModal.dailyBudget')}
                  initialValue={asin.AuthMaxMoney}
                  rules={[{ required: true, message: t('page.ailisting.newAsinModal.enterBudget') }]}
                >
                  <InputNumber
                    min={0}
                    step={10}
                    style={{ width: 120 }}
                  />
                </Form.Item>
              </div>
            ))}
            <div className="w-full flex justify-end">
              <Button
                loading={loading}
                onClick={() => setStep(1)}
                className="mr-4 mt-4"
              >
                {t('page.ailisting.newAsinModal.back')}
              </Button>
              <Button
                loading={loading}
                type="primary"
                htmlType="submit"
                className="mt-4"
              >
                {t('page.ailisting.newAsinModal.confirm')}
              </Button>
            </div>
          </>
        )}
      </Form>
    </Modal>
  );
};

export default NewAntdModalComponent;
