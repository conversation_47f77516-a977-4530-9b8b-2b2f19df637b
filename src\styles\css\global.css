@import './reset.css';
@import './nprogress.css';
@import './selectAcos.css';
@import './auth.css';
html,
body,
#root {
  height: 100%;
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


h1,
h2,
h3,
h4,
h5,
h6,
p,
span {
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
}

html {
  overflow-x: hidden;
}

* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.25) transparent;
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
  --ant-font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
}
.ant-table-header::-webkit-scrollbar {
  background-color: transparent;
}
html.grayscale {
  filter: grayscale(100%);
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}
::view-transition-old(root) {
  z-index: 9999;
}
::view-transition-new(root) {
  z-index: 1;
}
.dark::view-transition-old(root) {
  z-index: 1;
}
.dark::view-transition-new(root) {
  z-index: 9999;
}


.ant-table-column-title {
  font-size: 12px !important;
}

.ant-layout{
  background:  transparent !important;
}