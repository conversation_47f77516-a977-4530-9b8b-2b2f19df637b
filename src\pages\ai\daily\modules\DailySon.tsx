import { Form, Table, notification } from 'antd';
import BigNumber from 'bignumber.js';
import { useNavigate } from 'react-router-dom';
import { lazy, useRef, useState } from 'react';

import { AuthAsinList } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
const UserSearch = lazy(() => import('@/pages/ai/daily/modules/UserSearch'));
// 添加 props 类型定义
interface DailySonTableProps {
  type?: 'hosted' | 'nhosted';
}

const DailySonTable: React.FC<DailySonTableProps> = ({ type = 'hosted' }) => {
  const { t } = useTranslation();
  const [Loading, setLoading] = useState(false);
  const userInfo = useAppSelector(selectUserInfo);
  const [api, contextHolder] = notification.useNotification();
  // 存储表格数据
  const [tableData, setTableData] = useState([]);
  const { tableWrapperRef, scrollConfig } = useTableScroll();
  const tableRef = useRef(null);
  const [form] = Form.useForm();
  const countryCode = form.getFieldValue('country');
  const nav = useNavigate();

  // 处理from数据
  const handleFormData = () => {
    const values = form.getFieldsValue();

    return {
      UID: Number(userInfo.active_shop_id),
      CountryCode: values.country,
      // StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      // EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined,
      ManageState: type === 'nhosted' ? '3' : values.adStatus === 'undefined' ? undefined : values.adStatus,
      ParentAsin: values.parentAsin?.replace(/\s/g, '')
    };
  };

  // 处理ASIN数据类型切换
  const handleAsinDataTypeChange = (record: any) => {
    nav(`/ai/daily-detail?type=${type}&countryCode=${countryCode}&asin=${record.Asin}`);
  };

  const columns = [
    {
      title: t('page.dailyHosted.columns.country'),
      dataIndex: 'CountryCode',
      key: 'CountryCode',
      align: 'center'
    },
    {
      title: t('page.dailyHosted.columns.parentAsin'),
      dataIndex: 'Asin',
      key: 'Asin',
      align: 'center'
    },
    {
      title: t('page.dailyHosted.columns.status'),
      dataIndex: 'ManageState',
      key: 'ManageState',
      align: 'center',
      render: (text: string) => {
        // 0 申请托管 1 准备中 2 运行中 3 已经取消授权  4 申请取消授权  5取消中 6申请暂停 7 暂停中 8 已经暂停 9 申请恢复， a 恢复中
        // 状态映射
        const manageStateMap = {
          '0': { color: 'blue', label: t('page.dailyHosted.status.preparing') },
          '1': { color: 'blue', label: t('page.dailyHosted.status.preparing') },
          '2': { color: 'green', label: t('page.dailyHosted.status.running') },
          '3': { color: 'red', label: t('page.dailyHosted.status.cancelled') },
          '4': { color: 'orange', label: t('page.dailyHosted.status.cancelling') },
          '5': { color: 'orange', label: t('page.dailyHosted.status.cancelling') },
          '6': { color: 'volcano', label: t('page.dailyHosted.status.pausing') },
          '7': { color: 'volcano', label: t('page.dailyHosted.status.pausing') },
          '8': { color: 'volcano', label: t('page.dailyHosted.status.paused') },
          '9': { color: 'purple', label: t('page.dailyHosted.status.resuming') },
          a: { color: 'purple', label: t('page.dailyHosted.status.resuming') }
        };
        const state = manageStateMap[text];
        return <ATag color={state.color}>{state.label}</ATag>;
      }
    },
    {
      title: t('page.dailyHosted.columns.startDate'),
      dataIndex: 'LastUpdateDatetime',
      key: 'LastUpdateDatetime',
      align: 'center'
    },
    {
      title: t('page.dailyHosted.columns.operation'),
      dataIndex: 'op',
      key: 'op',
      align: 'center',
      // sorter: (a, b) => {
      //   const aValue = parseFloat(String(a.TACOS).replace('%', '') || 0);
      //   const bValue = parseFloat(String(b.TACOS).replace('%', '') || 0);
      //   return aValue - bValue;
      // },
      render: (_text: string, record: any) => (
        <AButton
          type="link"
          onClick={() => handleAsinDataTypeChange(record)}
        >
          {t('page.dailyHosted.buttons.viewDetails')}
        </AButton>
      )
    }
  ];

  const getTableData = async (params: any) => {
    console.log(params, 'form');
    setLoading(true);
    // console.log(values, "values");
    // console.log(type, "type");
    try {
      // 如果有ParentAsin 把键改为PAsin
      const req_data = {
        ...params,
        ...(params.ParentAsin ? { PAsin: params.ParentAsin } : {}),
        ...(type === 'nhosted'
          ? { ManageState: '3' }
          : {
              OnlyAuthed: true
            })
      };
      const res = await AuthAsinList(req_data);

      if (res && res.data && Object.keys(res.data.AsinAuthList).length > 0) {
        // 遍历res.data.data 如果有null 或者None 则赋值0
        //   const updatedData = res.data.data.map((item) => {
        //     const newItem = { ...item };
        //     for (const key in newItem) {
        //       if (newItem[key] === null || newItem[key] === 'None') {
        //         newItem[key] = '-';
        //       }
        //     }
        //     return newItem;
        //   });
        // 将res.data.AsinAuthList 转换为数组
        const updatedData = Object.values(res.data.AsinAuthList);
        // console.log(updatedData, "updatedData");

        setTableData(updatedData as any);
      } else {
        console.log('没有数据');
        setTableData([]);
      }
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    } catch (e) {
      setLoading(false);
      setTableData([]);
    }
  };

  const tableHeight = scrollConfig.y;

  return (
    <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      {contextHolder}
      <div className="sticky top-0 z-10 w-full">
        <ACard className="flex items-center justify-between">
          <UserSearch
            search={getTableData}
            reset={() => {}}
            form={form}
            hide={['dateRange']}
            loading={Loading}
          />
        </ACard>
      </div>

      <ACard
        title={
          <div className="flex items-center justify-between">
            <div className="">
              {type === 'hosted' ? t('page.dailyHosted.title.hosted') : t('page.dailyHosted.title.nhosted')}
            </div>
          </div>
        }
        className="flex-col-stretch sm:flex-1-hidden card-wrapper"
        ref={tableWrapperRef}
      >
        <div
          className="relative"
          style={{ minHeight: tableHeight }}
          ref={tableRef}
        >
          <Table
            columns={columns}
            dataSource={tableData}
            loading={Loading}
            size="small"
            rowKey={record => record.CountryCode + record.Asin}
            pagination={false}
            scroll={{ x: 'max-content', y: tableHeight }}
            locale={{
              emptyText: (
                <AEmpty
                  image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    !Loading && form.getFieldValue('country') ? (
                      <div className="flex-col items-center">
                        <AButton
                          type="primary"
                          className="my-2"
                          onClick={() => getTableData(handleFormData())}
                        >
                          {t('page.dailyHosted.buttons.refresh')}
                        </AButton>
                      </div>
                    ) : null
                  }
                ></AEmpty>
              )
            }}
          />
        </div>
      </ACard>
    </div>
  );
};

export default DailySonTable;
