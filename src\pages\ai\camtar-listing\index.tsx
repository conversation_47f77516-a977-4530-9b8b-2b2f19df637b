import { Suspense, lazy } from 'react';
import { Tag } from 'antd';
import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { getAsinCampaignSum } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
import BigNumber from "bignumber.js";
import { Icon } from '@iconify/react';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};
 
export function Component() {
    const { t } = useTranslation();

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const nav = useNavigate();

    const userInfo = useAppSelector(selectUserInfo);

    const [searchParams] = useSearchParams();

    const market = searchParams.get('market');

    
    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: getAsinCampaignSum,
            apiParams: {
                pagesize: 1000
            },
            immediate: false,
            columns: () => [
                {
                    key: '广告活动名称',
                    dataIndex: '广告活动名称',
                    title: t('page.camtarListing.columns.campaignName'),
                    align: 'center',
                    fixed: 'left',
                    width: 300,
                    checked: true,
                    render: (text: string) => {
                        return <ATooltip placement="topLeft" title={text}>
                        <div className='line-clamp-2 text-left' >
                            {text || "--"}
                        </div>
                    </ATooltip>
                        
                    }
                },
                {
                    key: '广告活动类型',
                    dataIndex: '广告活动类型',
                    title: t('page.camtarListing.columns.campaignType'),
                    align: 'center',
                    width: 80,
                    checked: true,
                    render: (text: string) => {
                        return <Tag >{text}</Tag>
                    }
                },
                {
                    key: '预算',
                    title: t('page.camtarListing.columns.budget'),
                    align: 'center',
                    dataIndex: '预算',
                    width: 100,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        // 不包含  totalRow['广告活动名称'] = "合计";
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.预算 - b.预算;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '状态',
                    dataIndex: '状态',
                    title: t('page.camtarListing.columns.status'),
                    align: 'center',
                    width: 100,
                    checked: true,
                    render: (text: string, record: any) => {
                        return (
                            <>
                                {

                                    // 有text 并且转换为大写 等于PAUSED 等于ENABLED显示绿色 其他显示红色
                                    text &&
                                    <>
                                        {
                                            text.toUpperCase() == 'PAUSED' && <Tag color='warning'>{t('page.camtarListing.status.paused')}</Tag>
                                        }
                                        {
                                            text.toUpperCase() == 'ENABLED' && <Tag color='green'>{t('page.camtarListing.status.enabled')}</Tag>
                                        }
                                        {
                                            text.toUpperCase() != 'PAUSED' && text.toUpperCase() != 'ENABLED' && <Tag color='red'>{t('page.camtarListing.status.archived')}</Tag>
                                        }
                                    </>
                                }
                            </>)
                    }
                },
                {
                    key: '订单_30天',
                    dataIndex: '订单_30天',
                    title: t('page.camtarListing.columns.orders30Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        console.log(a, b, "a.订单_30天, b.订单_30天")
                        return a.订单_30天 - b.订单_30天;
                    },
                },
                {
                    key: '花费_7天',
                    dataIndex: '花费_7天',
                    title: t('page.camtarListing.columns.spend7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.花费_7天 - b.花费_7天;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '点击_7天',
                    dataIndex: '点击_7天',
                    title: t('page.camtarListing.columns.clicks7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.点击_7天 - b.点击_7天;
                    },
                },
                {
                    key: '销售额_7天',
                    dataIndex: '销售额_7天',
                    title: t('page.camtarListing.columns.sales7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.销售额_7天 - b.销售额_7天;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: 'ACOS_7天',
                    dataIndex: 'ACOS_7天',
                    title: t('page.camtarListing.columns.acos7Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.ACOS_7天 - b.ACOS_7天;
                    },

                    //     if (isNaN(acosA) || !isFinite(acosA)) return 1;
                    //     if (isNaN(acosB) || !isFinite(acosB)) return -1;
                    //     return acosA - acosB;
                    // },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.花费_7天 || 0);
                        const sales = new BigNumber(record.销售额_7天 || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} /> ;
                    }
                },
                {
                    key: '花费_30天',
                    dataIndex: '花费_30天',
                    title: t('page.camtarListing.columns.spend30Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.花费_30天 - b.花费_30天;
                    },  
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '点击_30天',
                    dataIndex: '点击_30天',
                    title: t('page.camtarListing.columns.clicks30Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.点击_30天 - b.点击_30天;
                    },
                },
                {
                    key: 'ACOS_30天',
                    dataIndex: 'ACOS_30天',
                    title: t('page.camtarListing.columns.acos30Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.ACOS_30天 - b.ACOS_30天;
                    },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.花费_30天 || 0);
                        const sales = new BigNumber(record.销售额_30天 || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} /> ;
                    }
                },
                {
                    key: '销售额_30天',
                    dataIndex: '销售额_30天',
                    title: t('page.camtarListing.columns.sales30Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.销售额_30天 - b.销售额_30天;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '花费_3天',
                    dataIndex: '花费_3天',
                    title: t('page.camtarListing.columns.spend3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.花费_3天 - b.花费_3天;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: '点击_3天',
                    dataIndex: '点击_3天',
                    title: t('page.camtarListing.columns.clicks3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.点击_3天 - b.点击_3天;
                    },
                },
                {
                    key: '销售额_3天',
                    dataIndex: '销售额_3天',
                    title: t('page.camtarListing.columns.sales3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.销售额_3天 - b.销售额_3天;
                    },
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} /> ;
                    }
                },
                {
                    key: 'ACOS_3天',
                    dataIndex: 'ACOS_3天',
                    title: t('page.camtarListing.columns.acos3Days'),
                    align: 'center',
                    width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        if(a['广告活动名称'] == "合计" || b['广告活动名称'] == "合计"){
                            return 0;
                        }
                        return a.ACOS_3天 - b.ACOS_3天;
                    },
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.花费_3天 || 0);
                        const sales = new BigNumber(record.销售额_3天 || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} /> ;
                    }
                },
                {
                    key: '操作',
                    dataIndex: '操作',
                    title: t('page.camtarListing.columns.operation'),
                    align: 'center',
                    fixed: 'right',
                    checked: true,
                    width: 100,
                    render: (_, record) => {
                        return record.广告活动名称 != "合计" && <Suspense>
                           <div className='flex flex-center'>
                            <AButton type='link' 
                            onClick={() => navigateToListing(record)}
                            size="small"
                        
                        >
                            <div className='flex items-center'>
                            <Icon icon="fluent:apps-list-detail-24-regular" />
                            {t('page.camtarListing.buttons.details')}
                            </div>
                            </AButton>
                           </div>
                        </Suspense>

                }
            }
            ]
        },
        { showQuickJumper: true }
    );
    const [tableData, setTableData] = useState<any[]>(tableProps.dataSource);

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);

    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async (params: any = {}, isLocalSearch: boolean = false) => {
        if (isLocalSearch) {
            console.log(params, "本地搜索")
            // handledataSource(params);
            const newData = handledataSource(params);
            // 使用 setTableData 更新表格数据
            setTableData(newData);
        } else {
            if(searchParams.get('market') && searchParams.get('asin')){
            const query = {
                UID: userInfo.active_shop_id,
                CountryCode: searchParams.get('market'),
                EndDate: form.getFieldValue('date').format('YYYY-MM-DD'),
                ParentAsin: searchParams.get('asin'),
            };
            console.log(query, "query")
            // return
            run(query);
            }
        }
    }

    // 处理数据 加入汇总行
    // 广告活动名称 叫汇总
    // 预算为空
    // 状态为空
    // 花费加和 点击加和 销售额加和 订单加和
    // acos 需要根据加合后的花费和销售额计算
    const handledataSource = (params: any = {}) => {
        let filteredData = [...tableProps.dataSource];

        if (!filteredData.length) return [];

        // 本地搜索过滤
        if (params.campaignName) {
            console.log(params.campaignName, "params.campaignName");
            filteredData = filteredData.filter((item: any) => {
                return item.广告活动名称.includes(params.campaignName);
            });
        }
        if (params.campaignType) {
            console.log(params.campaignType, "params.campaignType");
            // 如果campaignType为NOT_DEEPBID，则过滤掉DEEPBID的活动
            // 如果campaignType为DEEPBID，则过滤掉非DEEPBID的活动
            // 如果campaignType为空，则不进行过滤
            if(params.campaignType == "NOT_DEEPBI"){
                filteredData = filteredData.filter((item: any) => {
                    return !item.广告活动名称.includes("DeepBI");
                });
            }else if(params.campaignType == "DEEPBI"){
                filteredData = filteredData.filter((item: any) => {
                    return item.广告活动名称.includes("DeepBI");
                });
            }else{
                // 不进行过滤
                filteredData = filteredData;
            }
        }
        if (params.state) {
            console.log(params.state, "params.state");
            filteredData = filteredData.filter((item: any) => {
                return item.状态.includes(params.state.toUpperCase());
            });
        }
        console.log(filteredData, "filteredData")
        const columnsKey = ['花费_7天', '点击_7天', '销售额_7天', '花费_30天', '点击_30天', '销售额_30天', '花费_3天', '点击_3天', '销售额_3天', '订单_30天'];

        // Initialize totals with BigNumber
        let totals: Record<string, BigNumber> = {};
        columnsKey.forEach(key => {
            totals[key] = new BigNumber(0);
        });

        // Sum up the values
        filteredData.forEach((item: any) => {
            columnsKey.forEach(key => {
                if (item[key]) {
                    totals[key] = totals[key].plus(new BigNumber(item[key]));
                }
            });
        });

        // Convert totals to plain numbers with two decimal places
        let totalRow: Record<string, any> = {};
        columnsKey.forEach(key => {
            if (key.includes('点击')) {
                // For '点击' columns, convert to integer
                totalRow[key] = totals[key].integerValue(BigNumber.ROUND_FLOOR).toString();
            } else {
                totalRow[key] = totals[key].toFixed(2);
            }
        });

        // Calculate ACOS for each period
        totalRow['ACOS_7天'] = totals['花费_7天'].dividedBy(totals['销售额_7天']).multipliedBy(100).toFixed(2) + "%";
        totalRow['ACOS_30天'] = totals['花费_30天'].dividedBy(totals['销售额_30天']).multipliedBy(100).toFixed(2) + "%";
        totalRow['ACOS_3天'] = totals['花费_3天'].dividedBy(totals['销售额_3天']).multipliedBy(100).toFixed(2) + "%";

        // Set the name for the total row
        totalRow['广告活动名称'] = t('page.camtarListing.summary');
        totalRow['ID'] = 100000;

        // Add the total row to the data
        const newData = [...filteredData, totalRow];
        return newData;
    }

    // 跳转广告活动详情
    const navigateToListing = (record: any) => {
        if(searchParams.get('market') && searchParams.get('asin')){
            const selection = window.getSelection();
            if (selection && selection.toString().length > 0) {
                return; // 如果有文本被选中，则不执行跳转
            }
            const market = searchParams.get('market');
            const asin = searchParams.get('asin');
            nav(`/ai/target-listing?market=${market}&asin=${asin}&id=${record.广告活动id}&name=${record.广告活动名称}`, '_blank');
            // router.push({name:"ai_target-listing",query:{market,asin,id:record.广告活动id,name:record.广告活动名称}})
        }
    }

    useEffect(() => {
      const newData = handledataSource({
        campaignType: 'DEEPBI',
      });
      // 使用 setTableData 更新表格数据
      setTableData(newData);
    }, [tableProps.dataSource])

    return (
        <div className="min-h-500px max-h-1000px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
            {/* <ACollapse
        bordered={false}
        className="card-wrapper"
        defaultActiveKey={['1']}
        items={[
          {
            key: '1',
            label: t('common.search'),
            children: (
              
            )
          }
        ]}
      /> */}
            <ACard >

                <UserSearch
                    search={getTableData}
                    localSearch={getTableData}
                    form={form}
                    loading={tableProps.loading}
                />
            </ACard>
            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={() => {
                            getTableData({})
                        }}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <div className='flex items-center cursor-default'>
                        <span className='mr-2'>
                            {searchParams.get('market') || ''}
                        </span>
                        |
                        <span className='ml-2 text-primary'>
                            {searchParams.get('asin') || ''}
                        </span>
                    </div>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={{
                        x: 702,
                        y: 700
                    }}
                    size="small"
                    {...tableProps}
                    dataSource={tableData}
                    showSizeChanger={false}
                    pagination={{ 
                        ...tableProps.pagination, 
                        showTotal: (total, range) => t('page.camtarListing.total', { total }),
                    }}
                    // pagination={
                    //     paginationConfig
                    // }
                    // onRow={(record, index) => {
                    //     return {
                    //         onClick: () => {
                                
                    //             if(searchParams.get('market') && searchParams.get('asin')){
                    //                 const selection = window.getSelection();
                    //                 if (selection && selection.toString().length > 0) {
                    //                     return; // 如果有文本被选中，则不执行跳转
                    //                 }
                    //                 const market = searchParams.get('market');
                    //                 const asin = searchParams.get('asin');
                    //                 nav(`/ai/target-listing?market=${market}&asin=${asin}&id=${record.广告活动id}&name=${record.广告活动名称}`, '_blank');
                    //                 // router.push({name:"ai_target-listing",query:{market,asin,id:record.广告活动id,name:record.广告活动名称}})
                    //             }
                    //         }
                    //     }
                    // }}
                    rowClassName={(record, index) => {
                        // Highlight the last row if it's the total row
                        if (tableData.length > 0) {
                            const isLastRow = index === tableData.length -1;
                            return isLastRow ? 'lastRowHigh' : '';
                        }
                        return '';
                    }}
                    locale={{ emptyText: <AEmpty
                        image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                        description={
                            !tableProps.loading ? (
                                <div className='flex-col items-center'>
                                    <AButton type='primary' className='my-2' onClick={reset}>
                                        {t('page.camtarListing.buttons.refresh')}
                                    </AButton>
                                </div>
                            ) : null
                    }></AEmpty> }}
                />
            </ACard>
        </div>
    );
}
