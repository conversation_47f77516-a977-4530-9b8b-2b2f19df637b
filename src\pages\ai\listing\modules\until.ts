// 目标ACOS选项 - 国际化版本
export const useAsinOptions = () => {
  const { t } = useTranslation();

  return [
    { value: 16, label: `ACOS ≤ 16% | ${t('page.ailisting.acosStrategy.strategyLabels.ultraConservative')}` },
    { value: 20, label: `ACOS ≤ 20% | ${t('page.ailisting.acosStrategy.strategyLabels.moderatelyConservative')}` },
    { value: 24, label: `ACOS ≤ 24% | ${t('page.ailisting.acosStrategy.strategyLabels.conservative')}` },
    { value: 28, label: `ACOS ≤ 28% | ${t('page.ailisting.acosStrategy.strategyLabels.balanced')}` },
    { value: 32, label: `ACOS ≤ 32% | ${t('page.ailisting.acosStrategy.strategyLabels.aggressive')}` },
    { value: 50, label: `ACOS ≤ 50% | ${t('page.ailisting.acosStrategy.strategyLabels.ultraAggressive')}` }
  ];
};

// 目标ACOS选项 - 兼容旧版本（非国际化）
export const asinOptions = [
  { value: 16, label: 'ACOS ≤ 16% | 极度保守' },
  { value: 20, label: 'ACOS ≤ 20% | 保守策略' },
  { value: 24, label: 'ACOS ≤ 24% | 适度保守' },
  { value: 28, label: 'ACOS ≤ 28% | 平衡策略' },
  { value: 32, label: 'ACOS ≤ 32% | 激进策略' },
  { value: 50, label: 'ACOS ≤ 50% | 极度激进' }
];

// 限制整数
export const limitDecimalsP = (value: any) => {
  const reg = /^(\d+).*$/;
  if (reg.test(value)) {
    return String(value).replace(reg, '$1');
  }
  return '';
};

// 获取ACOS策略标签 - 国际化版本
export const useGetStrategyLabel = () => {
  const { t } = useTranslation();

  return (params: { acosValue: number; options: any; selectedAcos: any; showacos?: boolean }) => {
    const { acosValue, options, selectedAcos, showacos = true } = params;
    // console.log(acosValue,options,selectedAcos)
    // 首先判断是否为自定义ACOS  selectedAcos是否在options中
    const NotCustom = options.find((option: any) => option.value === selectedAcos);
    if (NotCustom || !acosValue) {
      return '';
    }

    let label = '';
    if (acosValue <= 16) {
      label = `| ${t('page.ailisting.acosStrategy.strategyLabels.ultraConservative')}`;
    } else if (acosValue <= 20) {
      label = `| ${t('page.ailisting.acosStrategy.strategyLabels.moderatelyConservative')}`;
    } else if (acosValue <= 24) {
      label = `| ${t('page.ailisting.acosStrategy.strategyLabels.conservative')}`;
    } else if (acosValue <= 28) {
      label = `| ${t('page.ailisting.acosStrategy.strategyLabels.balanced')}`;
    } else if (acosValue <= 32) {
      label = `| ${t('page.ailisting.acosStrategy.strategyLabels.aggressive')}`;
    } else if (acosValue <= 50) {
      label = `| ${t('page.ailisting.acosStrategy.strategyLabels.ultraAggressive')}`;
    }

    if (showacos) {
      return `${acosValue}%${label}`;
    }
    return label;
  };
};

// 获取ACOS策略标签 - 兼容旧版本（非国际化）
export const getStrategyLabel = (params: {
  acosValue: number;
  options: any;
  selectedAcos: any;
  showacos?: boolean;
}) => {
  const { acosValue, options, selectedAcos, showacos = true } = params;
  // console.log(acosValue,options,selectedAcos)
  // 首先判断是否为自定义ACOS  selectedAcos是否在options中
  const NotCustom = options.find((option: any) => option.value === selectedAcos);
  if (NotCustom || !acosValue) {
    return '';
  }
  const label = '';
  // if (acosValue <= 16) {
  //     label = `| 极度保守`;
  // } else if (acosValue <= 20) {
  //     label = `| 保守策略`;
  // } else if (acosValue <= 24) {
  //     label = `| 适度保守`;
  // } else if (acosValue <= 28) {
  //     label = `| 平衡策略`;
  // } else if (acosValue <= 32) {
  //     label = `| 激进策略`;
  // } else if (acosValue <= 50) {
  //     label = `| 极度激进`;
  // }
  if (showacos) {
    return `${acosValue}%${label}`;
  }
  return label;
};
