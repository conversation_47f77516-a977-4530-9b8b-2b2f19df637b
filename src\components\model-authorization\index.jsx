import React, { useEffect, useState } from "react";
import { Form, Modal, Button, Image, Select, Checkbox } from 'antd';
import auth from '@/assets/imgs/auth.jpg';
import { useTranslation } from 'react-i18next';

export default function ModelAuthorization({ visible, onCancel, onOk,record, defaultRegion = 'NA' }) {
    const { t } = useTranslation();
    const [form] = Form.useForm();   
    const [region, setRegion] = useState("NA");
    const [CheckInitValue, setCheckInitValue] = useState([]);
    const [selectedCount, setSelectedCount] = useState(0);
    const regionData = [
        {
            label: '北美',
            value: 'NA',
            children: [
                { value: 'US', label: '美国' },
                { value: 'CA', label: '加拿大' },
                { value: 'MX', label: '墨西哥' },
                { value: 'BR', label: '巴西' }
            ],
        },
        {
            label: '欧洲',
            value: 'EU',
            children: [
                { value: 'UK', label: '英国' },
                { value: 'FR', label: '法国' },
                { value: 'DE', label: '德国' },
                { value: 'IT', label: '意大利' },
                { value: 'ES', label: '西班牙' },
                { value: 'NL', label: '荷兰' },
                { value: 'SE', label: '瑞典' },
                { value: 'PL', label: '波兰' },
                { value: 'BE', label: '比利时' },
                { value: 'IN', label: '印度' },
                { value: 'TR', label: '土耳其' },
                { value: 'IE', label: '爱尔兰' },
            ],
        },
        {
            label: '日本',
            value: 'JP',
            children: [
                { value: 'JP', label: '日本' },
            ],
        },
        {
            label: '新加坡',
            value: 'SG',
            children: [
                { value: 'SG', label: '新加坡' }
            ],
        },
        {
            label: '澳大利亚',
            value: 'AU',
            children: [
                { value: 'AU', label: '澳大利亚' }
            ],
        },
        {
            label: '沙特阿拉伯',
            value: 'SA',
            children: [
                { value: 'SA', label: '沙特阿拉伯' }
            ],
        },
        {
            label: '阿联酋',
            value: 'AE',
            children: [
                { value: 'AE', label: '阿联酋' }
            ],
        },
    ];
    const [options, setOptions] = useState([]);

    const handleCancel = () => {
        form.resetFields();  
        setOptions([]);
        setCheckInitValue([]);
        setRegion("");
        onCancel();
    };

    const handleSubmit = (values) => {
        const regionName = regionData.find(item => item.value === values.region)?.label;
        // 加入一个二次确认
        Modal.confirm({
            title: '确认您要授权的店铺以及区域',
            content: (
                <div className="py-4">
                    <p className="flex items-center">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">店铺：</span>
                        <span className="text-primary font-medium text-lg">{record.parent_shop || record.shop}</span>
                    </p>
                    <p className="flex items-center my-2">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">区域：</span>
                        <span className="text-primary font-medium text-lg">{regionName}</span>
                    </p>
                    <p className="flex items-center">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">国家：</span>
                        <span className="text-primary font-medium text-lg">{CheckInitValue.join(', ')}</span>
                    </p>
                </div>
            ),
            onOk: () => {
                onOk(values.region, CheckInitValue,"SP",record);
            }
        });
    };

    const handleSelectChange = (value) => {
        console.log(value, "value===");
        setRegion(value);
        const children = regionData.find(item => item.value === value)?.children;
        console.log(children, "children===");
        setOptions(children);
        
        // 如果是供应商账户，只选择第一个选项
        if (record.IsDistributor) {
            // console.log("供应商")
            // 如果有recordcountrycode 则选择recordcountrycode
            if (record.country) {
                // console.log(record.country, "record.country===");
                setCheckInitValue([record.country]);
            } else {
                // console.log(children[0].value, "children[0].value===");
                setCheckInitValue([children[0].value]);
            }
            // setSelectedCount(1);
        } else {
            // 不是供应商账户则选择所有选项
            setCheckInitValue(children.map(item => item.value));
        }
    };

    // 处理复选框变化，确保供应商账户只能选择一个选项
    const handleCheckboxChange = (checkedValues) => {
        if (record.IsDistributor) {
            // 供应商账户：必须且只能选择一个
            if (checkedValues.length === 0) {
                // 禁止清空选择，保留原有选择
                // window.$message?.warning('供应商账户至少选择一个站点');
                return; // 不更新状态，保持原有选择
            }
            
            // 找出新选择的选项
            const currentSelection = CheckInitValue[0]; // 当前选中的选项
            const newSelection = checkedValues.find(item => item !== currentSelection);
            
            // 如果是新增选项，则选中新选项；如果是取消选项，则保留剩余选项
            if (newSelection) {
                setCheckInitValue([newSelection]);
            } else {
                // 如果没有新增选项，说明是在取消当前选项，但我们不允许取消全部选项
                if (checkedValues.length > 0) {
                    setCheckInitValue(checkedValues);
                }
            }
            
            // setSelectedCount(1);
        } else {
            // 非供应商账户，可以多选
            setCheckInitValue(checkedValues);
            // setSelectedCount(checkedValues.length);
        }
    };

    useEffect(() => {
        // console.log(record,"record===")
        // console.log(defaultRegion,"defaultRegion===")
        handleSelectChange(defaultRegion);
    }, [defaultRegion, visible, record]);

    return (
        <Modal
            title={<div className="flex items-center pb-2">
                <span className="font-500 text-primary mr-1">店铺：{record.parent_shop || record.shop} </span>
                {t('page.setting.authmodel.title')}
                </div>}
            open={visible}
            onCancel={handleCancel}
            footer={null}
            width={1200}
            centered
        >
            <div className="flex flex-row">
                <div className="model-left pr-10">
                    <div className="mb-4 flex items-center">
                        <span className="font-medium mr-2">账号类型：</span>
                        <span className="text-primary font-medium">{record.IsDistributor ? 'VC' : 'SC'}</span>
                    </div>
                    <Form
                        layout="vertical"
                        onFinish={handleSubmit}
                        form={form}           
                        key={defaultRegion}
                        initialValues={{ region: defaultRegion }}
                    >
                        <Form.Item
                            label={t('page.setting.authmodel.regiontitle')}
                            name="region"
                            rules={[{ required: true, message: t('page.setting.authmodel.formrule') }]}
                        >
                            <Select
                                style={{ width: 280 }}
                                onChange={handleSelectChange}
                                placeholder={t('common.chooseText')}
                                disabled={record.isSon || (record.IsDistributor && record.children)}
                            >
                                {regionData.map((group, index) => (
                                    <Select.Option value={group.value} key={`${index}-${group.label}`}>
                                        {group.label}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>

                        {options.length > 0 && CheckInitValue.length > 0 && region && (
                            <Form.Item
                                label={record.IsDistributor ? '' : t('page.setting.authmodel.notifyText')}
                                name="notify"
                            >
                               <div>
                               <p className="hidden"> {JSON.stringify(options)}</p>
                                <Checkbox.Group
                                    options={options}
                                    value={CheckInitValue}
                                    disabled={record.isSon || record.children || !record.IsDistributor}
                                    onChange={handleCheckboxChange}
                                />
                               </div>
                            </Form.Item>
                        )}

                        <Form.Item>
                            <Button type="primary" htmlType="submit" className="mt-4 p-4 bg-primary">
                                {t('page.setting.authmodel.auth')}
                            </Button>
                        </Form.Item>

                        {/* 在这里写注释 */}
                        {record.IsDistributor && (
                          <div
                            className="bg-[#f0f5ff]  rounded-lg px-5 py-4 text-primary text-[14px] leading-7 font-medium shadow-sm"
                          >
                            <div className="text-[16px] font-semibold mb-2 flex items-center text-primary">
                              <span className="inline-block w-[6px] h-4 bg-primary rounded mr-2"></span>
                              Vendor Central（供应商）账号授权说明
                            </div>
                            <ul className="pl-5 m-0 list-decimal">
                              <li className="mb-1.5">VC 账号采用<strong>“单国家单店铺”</strong>模式，每个店铺仅支持选择一个国家进行授权。如需授权其他国家，可新建店铺分别授权。</li>
                              <li className="mb-1.5">授权国家选定后<strong>不可修改</strong>，若授权国家错误，可删除店铺后重新创建。</li>
                              <li className="mb-1.5">若授权失败，仅需重新进行店铺或广告授权。</li>
                            </ul>
                          </div>
                        )}
                    </Form>
                </div>
                <div className="model-right">
                    <div className="model-right-title">
                        <span>{t('page.setting.authmodel.step')}：</span>
                        <span className="text-gray-500 text-sm ml-2"></span>
                    </div>
                    <div className="model-right-content">
                        <div className="model-right-content-item flex items-center">
                            {/* 用圆圈包裹 */}
                            <span className="span flex items-center justify-center w-5 h-5 rounded-full bg-primary text-white mr-1">1</span>
                            <span className="flex-1">{t('page.setting.authmodel.step1')}</span>
                        </div>
                        <div className="model-right-content-item flex items-center">
                            {/* 用圆圈包裹 */}
                            <span className="span flex items-center justify-center w-5 h-5 rounded-full bg-primary text-white mr-1">2</span>
                            <span className="flex-1">小语种站点如日本，墨西哥等， 需要在亚马逊后台设置报告语言为英文才可正常获取数据，<a href="https://deepthought.feishu.cn/docx/TxfqdoMeLoH0s4xDoqhcybBJn1N#Etf4d7ostoUaV1xs92vcxKB5nrh" className="text-primary">如何设置？</a></span>
                        </div>
                        <div className="model-right-content-item flex items-center">
                        <span className="span flex items-center justify-center w-5 h-5 rounded-full bg-primary text-white mr-1">3</span>
                            <span className="flex-1">{t('page.setting.authmodel.step2')}</span>
                        </div>
                        <Image src={auth} alt="" />
                    </div>
                </div>
            </div>
        </Modal>
    );
}