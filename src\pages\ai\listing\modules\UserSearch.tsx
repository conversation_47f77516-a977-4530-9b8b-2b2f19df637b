import { Button, Col, Flex, Form, Input, Row, Select, DatePicker } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';

import { Icon } from '@iconify/react';
import { ActivedShopInfo } from '@/service/api';
import dayjs from 'dayjs';
import { selectUserInfo } from '@/store/slice/auth';
import { localStg } from '@/utils/storage';
import { getContinentAndCountryName } from '@/utils/useChartData';
interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
  hide?: string[];
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading,hide=[] }) => {
  const { t } = useTranslation();
  const [countries, setCountries] = useState([]);
  const [defaultCountry, setDefaultCountry] = useState<string | undefined>(undefined);
  const rangePresets = [
    { label: t('page.listingall.search.near3Days'), value: [dayjs().subtract(3, 'd'), dayjs().subtract(1, 'd')] },
    { label: t('page.listingall.search.near7Days'), value: [dayjs().subtract(7, 'd'), dayjs().subtract(1, 'd')] },
    { label: t('page.listingall.search.near30Days'), value: [dayjs().subtract(30, 'd'), dayjs().subtract(1, 'd')] },
  ];
  const [defaultDateRange, setDefaultDateRange] = useState(rangePresets[1].value); // 默认选择近七天
  const userInfo = useAppSelector(selectUserInfo);
  // 在组件顶部添加缓存有效期常量
  const COUNTRIES_CACHE_EXPIRY = 60 * 60 * 1000; // 1小时




  useEffect(() => {
    form.setFieldsValue({ dateRange: defaultDateRange }); // 设置默认时间范围
     // 获取带时间戳的缓存
    const cachedCountries = localStg.get('new_countries') as { data: any[]; timestamp: number };
    const now = Date.now();
    if (cachedCountries?.data?.length > 0 && now - cachedCountries.timestamp < COUNTRIES_CACHE_EXPIRY) {
      const localCountry = localStg.get('localCountry') as any;
      if (localCountry?.countryCode) {
        let { countryCode, continentCode } = localCountry;
        console.log(countryCode, continentCode,"localCountry====");
        setCountries(cachedCountries.data);
        setDefaultCountry(countryCode);
        form.setFieldsValue({ country: countryCode });
        handleSearch(cachedCountries.data,countryCode);
        return;
      }
    }
    ActivedShopInfo({
      lang: 'zh-CN'.split('-')[0],
    }).then((res: any) => {
      console.log(res.data, "res====");
      if (res && Object.keys(res.data).length > 0) {
        console.log(res.data.AuthCountry, "res.data.AuthCountry====");
        if(Object.keys(res.data.AuthCountry).length>0){
        let newCountries:any = []
        Object.values(res.data.AuthCountry).flatMap((item: any) =>{
          // console.log(item, "item====")
          if (item.IsAllow) {
            newCountries.push({
              ...item,
              label: t('page.setting.country.'+item.CountryCode),
              value: item.CountryCode,
              flagUrl: `circle-flags:${item.CountryCode.toLowerCase()}`,
            })
          }
        });
        newCountries.sort((a: any, b: any) => {
          const order = ['US', 'DE', 'UK', 'IT', 'FR', 'ES', 'JP', 'CN', 'IN']; // Added 'CN' and 'IN' as examples
          const indexA = order.indexOf(a.value);
          const indexB = order.indexOf(b.value);

          // If both countries are in the order list, sort by their index
          if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB;
          }
          // If only one country is in the order list, prioritize it
          if (indexA !== -1) return -1;
          if (indexB !== -1) return 1;
          // If neither country is in the order list, sort alphabetically
          return a.value.localeCompare(b.value);
        });
        newCountries.sort((a: any, b: any) => {
          return b.AdServiceActive - a.AdServiceActive; // Assuming IsActivated is a boolean or 0/1
        });
        setCountries(newCountries);
        if (newCountries.length > 0) {
          setDefaultCountry(newCountries[0].value);
          form.setFieldsValue({ country: newCountries[0].value });
          handleSearch();
        }
      }
    }
    });
  }, [form]);

  const handleReset = () => {
    form.resetFields();
    form.setFieldsValue({ country: defaultCountry, dateRange: defaultDateRange });
  };

  const handleSearch = (localCountries: any = [],countryCode: string = '') => {
    const values = form.getFieldsValue();
    let newCountries = countries;
    if(countryCode){
      values.country = countryCode;
    }
    if(localCountries.length > 0){
      newCountries = localCountries;
    }
    // console.log(values.country, 'values.country===')
    // console.log(newCountries, 'newCountries===')
    // let continent = newCountries.find((item: any) => item.value === values.country)?.ContinentCode || '';
    // console.log(continent, 'continent===')
    const params = {
      UID: userInfo.active_shop_id,
      CountryCode: values.country,
      StartDate: values.dateRange ? values.dateRange[0].format('YYYY-MM-DD') : undefined,
      EndDate: values.dateRange ? values.dateRange[1].format('YYYY-MM-DD') : undefined,
      ManageState: values.adStatus === 'undefined' ? undefined : values.adStatus,
      // 去除空格
      ParentAsin: values.parentAsin?.replace(/\s/g, ''),
    };
    localStg.set('localCountry', {
      countryCode: values.country,
      continentCode:t('page.setting.country.'+values.country)
    });
    search(params);
  };

  const handleDateChange = (value: any) => {
    const valueStart = dayjs(value[0]);
    const valueEnd = dayjs(value[1]);

    // 检查选择的时间范围是否与当前默认时间范围相同
    if (value && valueStart.isSame(defaultDateRange[0], 'day') && valueEnd.isSame(defaultDateRange[1], 'day')) {
      return; // 如果选择的时间范围与当前相同，则不更新
    }

    setDefaultDateRange(value);
    handleSearch();
  };

  return (
    <Form disabled={loading} form={form}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={6} lg={5}>
          <Form.Item className="m-0 text-start" name="country" label={t('page.listingall.search.country')}>
            <Select
              placeholder={t('page.listingall.search.selectCountry')}
              allowClear={false}
              onChange={(value: any) => handleSearch([],value)}
            >
              {countries.map((country: any) => (
                <Select.Option key={country.value} value={country.value} title={country.label}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Icon
                        className="mr-2"
                        icon={country.flagUrl}
                        width={22}
                        height={22}
                      />
                      {country.label}
                    </div>
                    {!!country.AdServiceActive && <ATag bordered={false} color="processing">{t('page.listingall.search.activated')}</ATag>}
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="parentAsin" label={
            <div className="flex items-center">
              {t('page.listingall.search.parentAsin')}
              <ATooltip title={t('page.listingall.tooltip.clickAsinDetails')}>
                <QuestionCircleOutlined className="ml-1" />
              </ATooltip>
            </div>
          }>
            <Input
              placeholder={t('page.listingall.search.inputParentAsin')}
              allowClear
              onPressEnter={(value: any) => handleSearch([],"")}
              onClear={() => handleSearch([],'')}
              suffix={
                <p onClick={() => handleSearch([],'')}>
                  <Icon icon="mdi:magnify" />
                </p>
              }
            />
          </Form.Item>
        </Col>

        {!hide.includes('adStatus') && (
          <Col span={24} md={12} lg={5}>
            <Form.Item className="m-0" name="adStatus" label={t('page.listingall.search.hostingStatus')}>
              <Select
                placeholder={t('page.listingall.search.selectHostingStatus')}
                onChange={(value: any) => handleSearch([], "")}
                defaultValue='undefined'
                options={[
                  { label: t('page.listingall.search.all'), value: 'undefined' },
                  { label: t('page.ailisting.status.preparing'), value: '1' },
                  { label: t('page.ailisting.status.running'), value: '2' },
                  { label: t('page.ailisting.status.paused'), value: '8' },
                  { label: t('page.ailisting.status.cancelling'), value: '3' },
                ]}
              />
            </Form.Item>
          </Col>
        )}

        <Col span={24} md={12} lg={7}>
          <Form.Item className="m-0" name="dateRange" label={t('page.listingall.search.dateRange')}>
            <DatePicker.RangePicker
              allowClear={false}
              format="YYYY-MM-DD"
              presets={rangePresets}
              disabledDate={(current: any) => {
                return current && current > dayjs().endOf('day');
              }}
              onChange={handleDateChange}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;